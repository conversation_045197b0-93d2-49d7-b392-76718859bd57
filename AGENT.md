# MyBya Mobile - Agent Guide

## Build/Test Commands
- **Build**: `dotnet build MyBya--Mobile/MyBya--Mobile.csproj`
- **Clean**: `dotnet clean MyBya--Mobile/MyBya--Mobile.csproj`
- **Restore**: `dotnet restore MyBya--Mobile/MyBya--Mobile.csproj`
- **Test**: No test project found - tests would use `dotnet test`
- **Run (specific platform)**: `dotnet run --project MyBya--Mobile/MyBya--Mobile.csproj --framework net8.0-android`

## Architecture
- **.NET 8 MAUI** cross-platform mobile app (Android/iOS/macOS/Windows)
- **Domain-Driven Service Layer** with generic BaseService<T,E> pattern
- **SQLite** local database with Entity Framework-style repositories
- **AutoMapper** for DTO/Entity mapping
- **Dependency Injection** via MauiAppBuilderExtension
- **Serilog** logging to console and file

## Code Style & Conventions
- **C# naming**: PascalCase for classes/methods, camelCase for fields/parameters
- **Nullable reference types** enabled (`<Nullable>enable</Nullable>`)
- **Implicit usings** enabled for cleaner imports
- **EditorConfig**: LF line endings, trim trailing whitespace
- **Services**: Organized by domain (Athletes, Members, TestCalendar), inherit from BaseService
- **DI Registration**: Transient for services, Singleton for handlers/repositories
- **XAML**: Code-behind files follow *.xaml.cs pattern
