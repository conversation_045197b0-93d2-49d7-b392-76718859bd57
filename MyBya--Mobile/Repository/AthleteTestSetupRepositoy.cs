using System;
using MyBya.DataAccess;
using MyBya.DbContext.Handler.Interface;
using MyBya.Entities;
using MyBya.Repository.Interface;

namespace MyBya.Repository;

public class AthleteTestSetupRepository : BaseRepository<AthleteTestSetupEntity>, IAthleteTestSetupRepository
{
    public AthleteTestSetupRepository(IDatabaseHandler<AthleteTestSetupEntity>
         databaseHandler) : base(databaseHandler)
    {

    }

    public async Task SetMostRecentTestSetup(AthleteTestSetupEntity entity)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        var item = await _databaseHandler
            .SelectByCriteria(x => x.Sport == entity.Sport);

        if (item.Count > 0)
        {
            var existingEntity = item.FirstOrDefault();
            if (existingEntity != null)
                entity.TestId = existingEntity.TestId;
            await Update(entity);
        }
        else
        {
            await Insert(entity);
        }
    }
}
