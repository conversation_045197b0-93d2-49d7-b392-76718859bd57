﻿using AutoMapper;
using MyBya.DbContext.Handler.Interface;
using MyBya.Entities;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Repository.Interface;
using MyBya.Shared;

namespace MyBya.Repository
{
	public class TestResultPaceChartRepository : BaseRepository<TestResultPaceChartEntity>, ITestResultPaceChartRepository
	{
		public TestResultPaceChartRepository(IDatabaseHandler<TestResultPaceChartEntity> databaseHandler)
			: base(databaseHandler)
		{

		}

        public async Task<TestResultPaceChartModel> GetPacesByInterval(string interval_distance)
		{
			string query = @$"SELECT *
                                FROM test_result_pace_chart
                               WHERE System = '{interval_distance}m'
							   AND Sport = {MyByaContext.Instance.CurrentTestSetup?.Sport ?? 0}";

			var result = await _databaseHandler.ExecuteQuery(query);

			if (result is null || !result.Any())
				throw new InvalidOperationException($"No pace-chart rows found for interval '{interval_distance}m'.");

			var entity = result.First();
			var mapper = ServiceHelper.GetService<IMapper>();
			return mapper.Map<TestResultPaceChartModel>(entity);
		}
	}
}
