﻿
using MyBya.DataAccess.Interfaces;
using MyBya.Entities;
using MyBya.DbContext.Handler.Interface;
using MyBya.DataAccess;

namespace MyBya.Repository
{
    public class TestResultHeartRateRepository : BaseRepository<TestResultHeartRatesEntity>, ITestResultHeartRateRepository
    {
		public TestResultHeartRateRepository(IDatabaseHandler<TestResultHeartRatesEntity> databaseHandler)
			: base(databaseHandler)
		{

        }
    }
}
