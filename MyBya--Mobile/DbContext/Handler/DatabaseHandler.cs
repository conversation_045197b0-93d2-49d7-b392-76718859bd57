using System;
using System.Diagnostics;
using System.Linq.Expressions;
using System.Threading.Tasks;
using MyBya.DbContext.Handler.Interface;
using MyBya.Entities;
using MyBya.Entities.Common;
using MyBya.Models.Common;
using Serilog;
using SQLite;

namespace MyBya.DbContext.Handler;

public class DatabaseHandler<E> : IDatabaseHandler<E> where E : BaseEntity, new()
{
    private async Task<SQLiteAsyncConnection> InitDatabaseAsync()
    {
        return await CreateTablesAsync();
    }

    public async Task<SQLiteAsyncConnection> CreateConnectionAsync()
    {
        return await InitDatabaseAsync();
    }

    private async Task<SQLiteAsyncConnection> CreateTablesAsync()
    {
        IList<Type> tableTypes = new List<Type>
        {
            typeof(TestResultHeartRatesEntity),
            typeof(TestResultPaceChartEntity),
            typeof(AthleteTestSetupEntity)
        };

        var connectionString = new SQLiteConnectionString(
            databasePath: DatabaseConstants.DatabasePath
        );

        var connection = new SQLiteAsyncConnection(connectionString);

        if (connection == null)
            throw new InvalidOperationException("Failed to create database connection");

        await connection.CreateTablesAsync(
            CreateFlags.None,
            tableTypes.ToArray()
        );

        return connection;
    }

    public async Task Delete(E entity)
    {
        try
        {
            var connection = await CreateConnectionAsync();
            await connection.DeleteAsync(entity);
            await connection.CloseAsync();
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task Insert(E entity)
    {
        try
        {
            var connection = await CreateConnectionAsync();
            await connection.InsertAsync(entity);
            await connection.CloseAsync();
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task InsertAll(List<E> entities)
    {
        try
        {
            var connection = await CreateConnectionAsync();
            await connection.RunInTransactionAsync((conn) => conn.InsertAll(entities));
            await connection.CloseAsync();
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task<IList<E>> SelectAllItems()
    {
        try
        {
            var connection = await CreateConnectionAsync();

            var result = await connection
                .Table<E>()
                .ToListAsync();

            await connection.CloseAsync();
            return result;
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task<IList<E>> SelectByCriteria(Expression<Func<E, bool>> predicate)
    {
        try
        {
            var connection = await CreateConnectionAsync();

            var result = await connection
                .Table<E>()
                .Where(predicate)
                .ToListAsync();

            await connection.CloseAsync();

            return result;
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task Update(E entity)
    {
        try
        {
            var connection = await CreateConnectionAsync();
            int execution = await connection.UpdateAsync(entity);
            await connection.CloseAsync();
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }

    }

    public async Task Clear()
    {
        try
        {
            var connection = await CreateConnectionAsync();
            await connection.RunInTransactionAsync((conn) => conn.DeleteAll<E>());
            await connection.CloseAsync();
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task<List<E>> ExecuteQuery(string query, params object[] values)
    {
        try
        {

#if DEBUG
            Debug.WriteLine($"Executing query: {query} with values: {values}");
#endif

            var connection = await CreateConnectionAsync();
            var result = await connection.QueryAsync<E>(query, values);
            await connection.CloseAsync();
            return result.ToList();
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }

    public async Task<bool> IsEmpty()
    {
        try
        {
            var connection = await CreateConnectionAsync();
            var count = await connection.Table<E>().CountAsync();
            await connection.CloseAsync();
            return count == 0;
        }
        catch (SQLiteException sqlException)
        {
            Log.Logger.Error(sqlException, sqlException.Message);
            throw;
        }
    }
}
