<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:MyBya"
             xmlns:converters="clr-namespace:MyBya.Converters"
             x:Class="MyBya.App">
    <Application.Resources>
        <ResourceDictionary>
            <converters:EnumDisplayConverter x:Key="enumDisplayConverter"/>
            <converters:StringToBoolConverter x:Key="StringToBoolConverter"/>
            <converters:BoolToPasswordVisibilityConverter x:Key="BoolToPasswordVisibilityConverter"/>
            <converters:BoolToSuccessColorConverter x:Key="BoolToSuccessColorConverter"/>
            <converters:IsNotNullOrEmptyConverter x:Key="IsNotNullOrEmptyConverter"/>
            <converters:InvertedBoolConverter x:Key="InvertedBoolConverter"/>
            <Style TargetType="Button" x:Key="DefaultButton">
                <Setter Property="BackgroundColor" Value="#FF7FE8" />
                <Setter Property="FontSize" Value="16" />
                <Setter Property="HeightRequest" Value="50" />
                <Setter Property="WidthRequest" Value="150" />
            </Style>

            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
