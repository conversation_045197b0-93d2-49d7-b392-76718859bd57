using System;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using MyBya.Ui.Views.Components;
using UIKit;

namespace MyBya.Platforms.iOS;

public class EntryHandleriOS
{
    public static void Init()
    {
        EntryHandler.Mapper.Add(nameof(EntryHandleriOS), (handler, view) =>
        {
            if (view is CEntry entry)
            {
                MauiTextField textField = handler.PlatformView;
                textField.Layer.CornerRadius = (nfloat)entry.CornerRadius;
                textField.Layer.BorderColor = entry.BorderColor.ToCGColor();
                textField.Layer.BorderWidth = 1;
                textField.BorderStyle = UITextBorderStyle.None;
            }
        });
    }
}
