using System;
using MyBya.Interfaces;
using Serilog;

namespace MyBya.Logging;

public class LogService : ILogService
{
    public void Initialize()
    {
        string logFilePath = Path.Combine(FileSystem.AppDataDirectory, "logs", "mybya_logs.txt");

        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .WriteTo.File(logFilePath, rollingInterval: RollingInterval.Day)
            .CreateLogger();
    }
}