using MyBya.Constants;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using MyBya.Shared;
using Serilog;

namespace MyBya.Services;

public class TestResultHeartRateService : BaseService<TestResultHeartRateModel, TestResultHeartRateDto>
{
    public TestResultHeartRateService()
    {
        apiUrl = string.Format(
            ApiKeys.GetURL("TestResultHeartRateURL"),
            MyByaContext.Instance.GetTestDetailAthleteId()
        );
    }

    public async Task<TestResultHeartRateModel?> GetTestResultHeartRate(int testDetailAthleteId = 0)
    {
        try
        {
            apiUrl = string.Format(
                ApiKeys.GetURL("TestResultHeartRateURL"),
                testDetailAthleteId > 0 ? testDetailAthleteId : MyByaContext.Instance.GetTestDetailAthleteId()
            );

            TestResultHeartRateDto? testResultHeartRateDto = await GetRequestWithModel();

            if (testResultHeartRateDto is null)
                return null;

            var model = new TestResultHeartRateModel
            {
                Id = testResultHeartRateDto.Id,
                TestDetailAthleteId = testResultHeartRateDto.TestDetailAthleteId,
                Zone1Max = testResultHeartRateDto.Zone1Max,
                Zone2Min = testResultHeartRateDto.Zone2Min,
                Zone2Max = testResultHeartRateDto.Zone2Max,
                Zone3Min = testResultHeartRateDto.Zone3Min,
                Zone3Max = testResultHeartRateDto.Zone3Max,
                CreatedAt = testResultHeartRateDto.CreatedAt,
                UpdatedAt = testResultHeartRateDto.UpdatedAt
            };

            return model;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            return null;
        }
    }
}
