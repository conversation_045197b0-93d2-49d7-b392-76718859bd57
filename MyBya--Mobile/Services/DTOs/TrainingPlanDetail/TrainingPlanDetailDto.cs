﻿using System;
using MyBya.Services.Common;

namespace MyBya.Services.DTOs
{
	public class TrainingPlanDetailDto : BaseDto
	{	
		public int? TrainingPlanCalendarId { get; set; }
        public DateTime? Date { get; set; }
        public int? TrainingPlanId { get; set; }
        public int? WorkoutId { get; set; }
        public string? WorkoutName { get; set; }
        public string? WorkoutTitle { get; set; }
        public string? WorkoutDescription { get; set; }
        public string? WorkoutIcon { get; set; }
        public int? WorkoutType { get; set; }
        public string? Comment { get; set; }
        public int? SystemAndPhasingId { get; set; }
        public int? ParentId { get; set; }
        public string? FieldName { get; set; }
        public string? FieldValue { get; set; }
        public string? Fixed { get; set; }
        public string? Configurable { get; set; }
        public bool? ForOneDay { get; set; }
        public bool? Complete { get; set; }
        public string? WorkoutParameter { get; set; }
	}
}

