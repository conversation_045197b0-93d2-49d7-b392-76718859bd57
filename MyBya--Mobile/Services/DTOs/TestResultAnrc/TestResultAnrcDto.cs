﻿using System;
using System.Text.Json.Serialization;
using MyBya.Services.Common;

namespace MyBya.Services.DTOs
{
	public class TestResultAnrcDto : BaseDto
	{
		[JsonPropertyName("testDetailAthleteId")]
        public int? TestDetailAthleteId { get; set; }

        [JsonPropertyName("system")]
        public string? System { get; set; }

        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("anrc1")]
        public string? Anrc1 { get; set; }

        [JsonPropertyName("anrc2")]
        public string? Anrc2 { get; set; }

        [JsonPropertyName("createdAt")]
        public DateTime? CreatedAt { get; set; }

        [JsonPropertyName("updatedAt")]
        public DateTime? UpdatedAt { get; set; }
	}
}

