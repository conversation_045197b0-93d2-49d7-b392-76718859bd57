using System;
using System.Text.Json.Serialization;
using MyBya.Services.Common;

namespace MyBya.Services.DTOs;

public class TestDataEntryDto : BaseDto
{
    [JsonPropertyName("testDetailAthleteId")]
    public int? TestDetailAthleteId { get; set; }

    [JsonPropertyName("interval")]
    public int? Interval { get; set; }

    [JsonPropertyName("intervalDistance")]
    public int? IntervalDistance { get; set; }

    [JsonPropertyName("intervalTime")]
    public int? IntervalTime { get; set; }

    [JsonPropertyName("intervalSecond")]
    public int? IntervalSecond { get; set; }

    [JsonPropertyName("heartRate")]
    public int? HeartRate { get; set; }

    [JsonPropertyName("blac")]
    public double? Blac { get; set; }

    [JsonPropertyName("intervalWatts")]
    public int? IntervalWatts { get; set; }
}