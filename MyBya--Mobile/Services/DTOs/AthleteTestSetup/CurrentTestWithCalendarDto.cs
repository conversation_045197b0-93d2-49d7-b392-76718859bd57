using MyBya.Enums;
using MyBya.Services.Common;

namespace MyBya.Services.DTOs;

public class CurrentTestWithCalendarDto : BaseDto
{
    public SportEnum? Sport { get; set; }
    public string Time { get; set; }
    public short? Systems { get; set; }

    // TestCalendar properties
    public DateTime CalendarDate { get; set; }
    public SportEnum CalendarSport { get; set; }

    public bool CanRetake
    {
        get
        {
            var today = DateTime.Now;
            return CalendarDate - today >= TimeSpan.FromDays(30);
        }
    }

    public string CanRetakeDate
    {
        get
        {
            var canRetakeDate = CalendarDate.AddDays(30);
            return canRetakeDate.ToString("M/d/yyyy");
        }
    }
}
