﻿using System;
using System.Text.Json.Serialization;
using MyBya.Services.Common;

namespace MyBya.Services.DTOs
{
	public class TestResultPaceChartDto : BaseDto
	{
		[JsonPropertyName("testDetailAthleteId")]
        public int? TestDetailAthleteId { get; set; }

        [JsonPropertyName("system")]
        public string? System { get; set; }

        [JsonPropertyName("arc1")]
        public string? Arc1 { get; set; }

        [JsonPropertyName("arc2")]
        public string? Arc2 { get; set; }

        [JsonPropertyName("arc3")]
        public string? Arc3 { get; set; }

        [JsonPropertyName("ltcc")]
        public string? Ltcc { get; set; }

        [JsonPropertyName("createdAt")]
        public DateTime? CreatedAt { get; set; }

        [JsonPropertyName("updatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [JsonPropertyName("data")]
        public string? Data { get; set; }
	}
}

