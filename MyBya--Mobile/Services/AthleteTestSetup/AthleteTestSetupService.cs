using MyBya.Constants;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using MyBya.Shared;
using Newtonsoft.Json;
using Serilog;

namespace MyBya.Services;

public class AthleteTestSetupService : BaseService<AthleteTestSetupModel, AthleteTestSetupResponseDto>
{
    public async Task<AthleteTestSetupModel?> CreateAthleteTest(AthleteTestSetupDto item)
    {
        try
        {
            apiUrl = ApiKeys.GetURL("AthleteTestSetupURL") + "test-setup-calendar";
            var dto = await PostRequestWithModel(JsonConvert.SerializeObject(item));
            return Mapper.Map<AthleteTestSetupModel>(dto);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            return null;
        }
    }

    public async Task<AthleteTestSetupModel?> GetAthleteTestById(int id)
    {
        try
        {
            var requestedUrl = apiUrl + $"{ApiKeys.GetURL("AthleteTestSetupURL")}{id}";
            apiUrl = requestedUrl;
            var dto = await GetRequestWithModel();
            return Mapper.Map<AthleteTestSetupModel>(dto);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            return null;
        }
    }

    public async Task<List<AthleteTestSetupModel>> GetMostRecentTestsByMemberId(int memberId)
    {
        try
        {
            apiUrl = ApiKeys.GetURL("TestSetupMostRecentTestsByMember") + "?MemberId=" + memberId;
            var dto = await GetListRequestWithModel();
            return Mapper.Map<List<AthleteTestSetupModel>>(dto);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            return new List<AthleteTestSetupModel>();
        }
    }
}
