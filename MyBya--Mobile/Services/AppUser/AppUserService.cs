using System;
using MyBya.Constants;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;

namespace MyBya.Services;

public class AppUserService : BaseService<AppUserModel, AppUserDto>
{
    public AppUserService()
    {
        apiUrl = ApiKeys.GetURL("AppUserServiceURL");
    }

    public async Task<AppUserModel?> GetUser(int id) 
    {
        apiUrl = string.Format(apiUrl, id);

        var dto = await GetRequestWithModel();
        var model = Mapper.Map<AppUserModel>(dto);
        return model;
    }
}
