using MyBya.Constants;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;

namespace MyBya.Services;

public class MembersService : BaseService<MemberModel, MemberServiceDto>
{
    public MembersService()
    {
        apiUrl = ApiKeys.GetURL("MemberServiceURL");
    }

    public async Task<List<MemberModel>> GetMembers() 
    {
        apiUrl = string.Format(apiUrl, "", "0", "5");

        string json = await GetRequest();
        var response = GetApiResponseList<MemberServiceDto>(json);

        var models = new List<MemberModel>();

        foreach (var item in response)
        {
            var model = Mapper.Map<MemberModel>(item);
            models.Add(model);
        }
        
        return models;
    }
}
