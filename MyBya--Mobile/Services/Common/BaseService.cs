using System;
using System.Diagnostics;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using AutoMapper;
using MyBya.Constants;
using MyBya.Extensions;
using MyBya.Helpers;
using MyBya.Interfaces;
using MyBya.Models;
using MyBya.Models.Api;
using MyBya.Models.Common;
using MyBya.Services.DTOs;
using Newtonsoft.Json;
using Serilog;

namespace MyBya.Services.Common;

public abstract class BaseService<T, E> where T : BaseModel where E : BaseDto
{
    private HttpClient _client { get; set; }
    protected string apiUrl { get; set; } = string.Empty;
    protected IMapper Mapper { get; set; }

    public BaseService()
    {
#if DEBUG
        var handlerDebug = new HttpClientHandler
        {
            ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) => true
        };

        _client = new HttpClient(handlerDebug)
        {
            BaseAddress = new Uri("https://localhost:44365")
        };
#else
       var handler = new HttpClientHandler
       {
           ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) => true
       };

       _client = new HttpClient(handler);
#endif

        //_client = new HttpClient();
        Mapper = ServiceHelper.GetService<IMapper>();
    }

    protected async Task<E?> GetRequestWithModel()
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {apiUrl} <=====");
#endif

            var response = await _client.GetAsync(apiUrl);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    var result = await GetJsonResponseWithModel(response);
                    if (result == null)
                    {
                        throw new Exception("Received null response from GetJsonResponseWithModel.");
                    }
                    return result;
            }

            string errorMessage = await GetJsonResponse(response);
            throw new Exception($"Error on Get request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<List<E>> GetListRequestWithModel()
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {apiUrl} <=====");
#endif
            var response = await _client.GetAsync(apiUrl);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonListResponseWithModel(response);
            }

            string errorMessage = await GetJsonResponse(response);
            throw new Exception($"Error on Get request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<string> GetRequest()
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {apiUrl} <=====");
#endif
            var response = await _client.GetAsync(apiUrl);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);
            }

            string errorMessage = await GetJsonResponse(response);
            throw new Exception($"Error on Get request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<E> PostRequestWithModel(string contentJson)
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {contentJson} <=====");
#endif
            StringContent content = GetStringContent(contentJson);

            HttpResponseMessage response = await _client.PostAsync(apiUrl, content);

            switch (response.StatusCode)
            {
                case HttpStatusCode.Created:
                case HttpStatusCode.OK:
                    var result = await GetJsonResponseWithModel(response);
                    if (result == null)
                    {
                        throw new Exception("Received null response from GetJsonResponseWithModel.");
                    }
                    return result;

                case HttpStatusCode.BadRequest:
                    return default(E)!;

                default:
                    var error = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Error: {response.StatusCode} - {error}");
                    return default(E)!;
            }
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<string> PostRequest(string contentJson)
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {contentJson} <=====");
#endif
            StringContent content = GetStringContent(contentJson);

            HttpResponseMessage response = await _client.PostAsync(apiUrl, content);

            switch (response.StatusCode)
            {
                case HttpStatusCode.Created:
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);

                case HttpStatusCode.BadRequest:
                    return "";
            }

            throw new Exception($"Error on post request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<LoginResponse> LoginPostRequest(LoginRequestDto loginDto)
    {
        try
        {
            var parameters = new Dictionary<string, string>
            {
                { "grant_type", "password" },
                { "username", loginDto.UserNameOrEmailAddress ?? "" },
                { "password", loginDto.Password ?? "" },
                { "client_id", "MyByaApi_App" },
                { "scope", "MyByaApi offline_access" }
            };

            _client.DefaultRequestHeaders.Clear();
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            var response = await _client.PostAsync(apiUrl, new FormUrlEncodedContent(parameters));

            var loginResponse = new LoginResponse
            {
                StatusCode = response.StatusCode
            };

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    loginResponse.Token = await GetJsonResponse(response);
                    loginResponse.Success = true;
                    return loginResponse;

                case HttpStatusCode.BadRequest:
                case HttpStatusCode.Unauthorized:
                    loginResponse.Success = false;
                    loginResponse.ErrorMessage = "Invalid username or password";
                    return loginResponse;

                default:
                    loginResponse.Success = false;
                    loginResponse.ErrorMessage = $"Unexpected error occurred. Status code: {response.StatusCode}";
                    var error = await response.Content.ReadAsStringAsync();
                    Log.Logger.Error("Login failed with status code {StatusCode}. Error: {Error}", response.StatusCode, error);
                    return loginResponse;
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Login request failed");
            return new LoginResponse
            {
                Success = false,
                ErrorMessage = "Unable to connect to the server. Please try again later.",
                Exception = ex
            };
        }
    }

    protected async Task<string> PutRequest(string? contentJson = null)
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {contentJson} <=====");
#endif
            StringContent? content = contentJson is null ? null : GetStringContent(contentJson);

            HttpResponseMessage response = await _client.PutAsync(apiUrl, content);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);

                case HttpStatusCode.Unauthorized:
                    response = await _client.PutAsync(apiUrl, content);
                    return await GetJsonResponse(response);
            }

            throw new Exception($"Error on Put request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<string> DeleteRequest(string contentJson)
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {contentJson} <=====");
#endif
            var request = new HttpRequestMessage
            {
                Method = HttpMethod.Delete,
                RequestUri = new Uri(apiUrl),
                Content = GetStringContent(contentJson)
            };

            var response = await _client.SendAsync(request);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);

                case HttpStatusCode.Unauthorized:
                    HttpRequestMessage refreshRequest = RefreshDeleteRequest(contentJson);
                    response = await _client.SendAsync(refreshRequest);
                    return await GetJsonResponse(response);
            }

            throw new Exception($"Error on Delete request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<string> UploadFile(byte[] content, string fileName)
    {
        using var formData = new MultipartFormDataContent();
        var fileStream = new MemoryStream(content);
        var fileContent = new StreamContent(fileStream);
        fileContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
        formData.Add(fileContent, "file", fileName);

        var response = await _client.PostAsync(apiUrl, formData);

        switch (response.StatusCode)
        {
            case HttpStatusCode.Created:
            case HttpStatusCode.OK:
                return await GetJsonResponse(response);

            case HttpStatusCode.Unauthorized:
                using (var newFormData = new MultipartFormDataContent())
                {
                    var newFileStream = new MemoryStream(content);
                    var newFileContent = new StreamContent(newFileStream);
                    newFileContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                    newFormData.Add(newFileContent, "file", fileName);
                    response = await _client.PostAsync(apiUrl, newFormData);
                }
                return await GetJsonResponse(response);
        }

        string errorMessage = await GetJsonResponse(response);
        throw new Exception($"Error on uploading image, error: {errorMessage}, status code: {response.StatusCode}");
    }

    protected StringContent GetStringContent(string content)
    {
        return new StringContent(
            content: content,
            encoding: Encoding.UTF8,
            mediaType: "application/json"
        );
    }

    protected virtual string SerializeEntity<EE>(EE instance)
    {
        return JsonConvert.SerializeObject(instance);
    }

    protected virtual E GetEntity(T model)
    {
        throw new NotSupportedException("Derived classes must implement GetEntity");
    }

    private async Task<string> GetJsonResponse(HttpResponseMessage response)
    {
        string jsonResponse = await response.Content.ReadAsStringAsync();
#if DEBUG
        Debug.WriteLine($" ====> API RESPONSE: {jsonResponse} <=====");
#endif
        return jsonResponse;
    }

    private async Task<E?> GetJsonResponseWithModel(HttpResponseMessage response)
    {
        string jsonResponse = await GetJsonResponse(response);

        if (!string.IsNullOrEmpty(jsonResponse))
        {
            E? model = JsonConvert
                .DeserializeObject<E>(jsonResponse);

            return model!;
        }

        return default(E);
    }

    private async Task<List<E>> GetJsonListResponseWithModel(HttpResponseMessage response)
    {
        string jsonResponse = await GetJsonResponse(response);

        if (!string.IsNullOrEmpty(jsonResponse))
        {
            List<E> model = JsonConvert
                .DeserializeObject<List<E>>(jsonResponse) ?? new List<E>();

            return model!;
        }

        return new List<E>();
    }

    protected virtual List<EE> GetApiResponseList<EE>(string json)
        where EE : new()
    {
        ApiResponseList<EE>? apiResponse = JsonConvert
            .DeserializeObject<ApiResponseList<EE>>(json);

        return apiResponse?.Items ?? new List<EE>();
    }

    private HttpRequestMessage RefreshDeleteRequest(string contentJson)
    {
        var refreshRequest = new HttpRequestMessage();
        refreshRequest.Method = HttpMethod.Delete;
        refreshRequest.RequestUri = new Uri(apiUrl);
        refreshRequest.Content = GetStringContent(contentJson);
        return refreshRequest;
    }

    private async Task<string> GetTokenAsync()
    {
        string token = await ServiceHelper
            .GetService<IAuthenticationService>()
            .GetTokenAsync() ?? string.Empty;

        string expirationToken = await ServiceHelper
            .GetService<IAuthenticationService>()
            .GetTokenExpirationValue() ?? string.Empty;

#if DEBUG
        Debug.WriteLine($" ====> TOKEN: {token} <=====");
        Debug.WriteLine($" ====> EXPIRATION: {expirationToken} <=====");
#endif

        return token;
    }
}
