using MyBya.Interfaces;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using MyBya.Constants;
using Serilog;
using Newtonsoft.Json;
using System.IdentityModel.Tokens.Jwt;

namespace MyBya.Services;

public class AuthenticationService : BaseService<LoginModel, LoginDto>, IAuthenticationService
{
    private readonly ISecureStorage _secureStorage;
    private const string UserTokenKey = "jwt_token";
    private const string UserRefreshTokenKey = "refresh_token";
    private const string TokenExpirationKey = "token_expiration";

    public AuthenticationService()
    {
        _secureStorage = SecureStorage.Default;
    }

    public async Task<LoginModel?> LoginAsync(string username, string password, bool rememberMe = true)
    {
        try
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                return null;

            apiUrl = ApiKeys.GetURL("LoginURL");

            var loginDto = new LoginRequestDto
            {
                UserNameOrEmailAddress = username,
                Password = password,
                RememberMe = rememberMe
            };

            var response = await LoginPostRequest(loginDto);

            if (!response.Success)
            {
                Log.Logger.Error("Login failed: {ErrorMessage}", response.ErrorMessage);
                return new LoginModel
                {
                    Success = false,
                    Description = response.ErrorMessage ?? "An unexpected error occurred"
                };
            }

            if (response.Token == null)
            {
                Log.Logger.Error("Login succeeded but no token was received");
                return new LoginModel
                {
                    Success = false,
                    Description = "Authentication failed: No token received"
                };
            }

            await StoreTokenAsync(response.Token);

            return new LoginModel
            {
                Success = true,
                Description = "Login successful"
            };
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Login failed with exception");
            return new LoginModel
            {
                Success = false,
                Description = "An unexpected error occurred. Please try again later."
            };
        }
    }

    public Task<bool> RegisterAsync(string username, string email, string password)
    {
        // TODO: Implement actual registration logic
        return Task.FromResult(true);
    }

    public async Task<bool> ForgotPasswordAsync(string email)
    {
        try
        {
            if (string.IsNullOrEmpty(email))
            {
                return false;
            }

            // TODO: Implement actual password reset API call
            Log.Logger.Information("Password reset requested");
            return true;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error during password reset");
            return false;
        }
    }

    public Task<bool> LogoutAsync()
    {
        try
        {
            _secureStorage.Remove(UserTokenKey);
            _secureStorage.Remove(UserRefreshTokenKey);
            _secureStorage.Remove(TokenExpirationKey);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Logout failed");
            return Task.FromResult(false);
        }
    }

    public async Task<string?> GetTokenAsync()
    {
        try
        {
            return await _secureStorage.GetAsync(UserTokenKey);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error retrieving token");
            return null;
        }
    }

    private async Task StoreTokenAsync(string token)
    {
        try
        {
            var tokenModel = System.Text.Json.JsonSerializer.Deserialize<TokenModel>(token);

            if (tokenModel != null)
            {
                await _secureStorage.SetAsync(UserTokenKey, tokenModel.AccessToken);
                await _secureStorage.SetAsync(UserRefreshTokenKey, tokenModel.RefreshToken);

                var expirationTime = DateTime.UtcNow.AddSeconds(tokenModel.ExpiresIn);
                await _secureStorage.SetAsync(TokenExpirationKey, expirationTime.ToString("O"));
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error storing token");
            throw;
        }
    }

    public async Task<bool> IsUserLoggedInAsync()
    {
        try
        {
            var token = await _secureStorage.GetAsync(UserTokenKey);

            if (string.IsNullOrEmpty(token))
                return false;

            var expirationTimeStr = await _secureStorage.GetAsync(TokenExpirationKey);

            if (string.IsNullOrEmpty(expirationTimeStr))
                return false;

            if (DateTime.TryParse(expirationTimeStr, out DateTime expirationTime))
                return DateTime.Now < expirationTime;

            return false;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error checking token validity");
            return false;
        }
    }

    public async Task<string?> GetTokenExpirationValue()
    {
        try
        {
            return await _secureStorage.GetAsync(TokenExpirationKey);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error retrieving token expiration");
            return null;
        }
    }

    private Dictionary<string, string> ParseJwtClaims(string token)
    {
        var handler = new JwtSecurityTokenHandler();

        if (handler.ReadToken(token) is JwtSecurityToken jsonToken)
            return jsonToken.Claims.ToDictionary(c => c.Type, c => c.Value);

        throw new Exception("Error on decoding JWT token");
    }
}
