using MyBya.Constants;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using Serilog;

namespace MyBya.Services;

public class UserTrainingPlanService : BaseService<UserTrainingPlanModel, UserTrainingPlanDto>
{
    public async Task<List<TrainingPlanTemplateModel>> GetTrainingPlanByMemberId(int memberId)
    {
        try
        {
            string baseUrl = ApiKeys.GetURL("UserTrainingPlanURL");

            if (string.IsNullOrEmpty(baseUrl))
                throw new InvalidOperationException("UserTrainingPlanURL configuration is missing or empty.");

            apiUrl = baseUrl + memberId;
            var requestResult = await GetRequest();

            if (requestResult == null)
                return [];

            List<UserTrainingPlanDto> dtos = GetApiResponseList<UserTrainingPlanDto>(requestResult);

            if (dtos == null || dtos?.Count == 0)
                return [];

            var trainingPlanTemplates = new List<TrainingPlanTemplateModel>();

            foreach (var dto in dtos ?? [])
            {
                if (Mapper == null)
                    throw new InvalidOperationException("Mapper is not configured.");

                var userTrainingPlanTemplate = Mapper.Map<UserTrainingPlanModel>(dto);

                var trainingPlanTemplate = userTrainingPlanTemplate.TrainingPlanTemplate;

                if (trainingPlanTemplate == null)
                    continue;

                trainingPlanTemplate.EndDate = userTrainingPlanTemplate.EndDate;
                trainingPlanTemplates.Add(trainingPlanTemplate);
            }

            return trainingPlanTemplates;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            return [];
        }
    }
}
