using MyBya.Constants;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using MyBya.Shared;
using Newtonsoft.Json;
using Serilog;

namespace MyBya.Services;

public class CurrentTestWithCalendarService : BaseService<CurrentTestWithCalendarModel, CurrentTestWithCalendarDto>
{
    public async Task<List<CurrentTestWithCalendarDto>?> GetCurrentTestsWithCalendar()
    {
        try
        {
            int memberId = MyByaContext.Instance.GetMemberId();
            apiUrl = $"{ApiKeys.GetURL("CurrentTestsWithCalendarURL")}?MemberId={memberId}";

            var tests = await GetListRequestWithModel();

            Log.Logger.Information("Successfully retrieved {Count} current tests for member {MemberId}",
                tests.Count, memberId);

            return tests;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Failed to retrieve current tests with calendar for member {MemberId}. Error: {Message}",
                MyByaContext.Instance.GetMemberId(), ex.Message);
            return null;
        }
    }
}
