using System;
using MyBya.Constants;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using Newtonsoft.Json;

namespace MyBya.Services.Terra;

public class TerraAuthService : BaseService<TerraAuthModel, TerraAuthRequestDto>
{
    public async Task<TerraAuthModel> GetTerraAuthModelAsync(TerraAuthRequestDto terraAuthRequestDto)
    {
        if (terraAuthRequestDto == null)
        {
            throw new ArgumentNullException(nameof(terraAuthRequestDto), "Terra authentication request data cannot be null.");
        }

        apiUrl = ApiKeys.GetURL("TerraAuthURL");

        string response = await PostRequest(JsonConvert.SerializeObject(terraAuthRequestDto));

        if (string.IsNullOrEmpty(response))
        {
            throw new InvalidOperationException("Received empty response from Terra authentication service.");
        }

        TerraAuthDto? dto = JsonConvert.DeserializeObject<TerraAuthDto>(response);

        if (dto == null)
        {
            throw new JsonSerializationException($"Failed to deserialize Terra authentication response: {response}");
        }

        return Mapper.Map<TerraAuthModel>(dto);
    }
}
