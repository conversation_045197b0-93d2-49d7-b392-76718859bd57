﻿using MyBya.Constants;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using MyBya.Shared;
using Serilog;

namespace MyBya.Services
{
	public class TestResultPaceChartService : BaseService<TestResultPaceChartModel, TestResultPaceChartDto>
	{
		public TestResultPaceChartService()
		{
			apiUrl = string.Format(
				ApiKeys.GetURL("TestResultPaceChartURL"),
                MyByaContext.Instance.GetTestDetailAthleteId()
            );
		}

		public async Task<List<TestResultPaceChartModel>> GetPaceCharts(int testDetailAthleteId = 0)
		{
			try
			{
                apiUrl = string.Format(
                    ApiKeys.GetURL("TestResultPaceChartURL"),
                    testDetailAthleteId > 0 ? testDetailAthleteId : MyByaContext.Instance.GetTestDetailAthleteId()
                );

				List<TestResultPaceChartDto> paceCharts = await GetListRequestWithModel();

				if (paceCharts is null)
					return new List<TestResultPaceChartModel>();

				var paceChartModels = new List<TestResultPaceChartModel>();

				foreach (TestResultPaceChartDto paceChart in paceCharts)
				{
					var model = new TestResultPaceChartModel
					{
						Id = paceChart.Id,
						TestDetailAthleteId = paceChart.TestDetailAthleteId,
						Sport = MyByaContext.Instance.CurrentTestSetup?.Sport,
						System = paceChart.System,
						Arc1 = paceChart.Arc1,
						Arc2 = paceChart.Arc2,
						Arc3 = paceChart.Arc3,
						Ltcc = paceChart.Ltcc,
						CreatedAt = paceChart.CreatedAt,
						UpdatedAt = paceChart.UpdatedAt,
						Data = paceChart.Data
					};

					paceChartModels.Add(model);
				}

				return paceChartModels;
			}
			catch (Exception ex)
			{
				Log.Logger.Error(ex, ex.Message);
				return new List<TestResultPaceChartModel>();
			}
        }
	}
}

