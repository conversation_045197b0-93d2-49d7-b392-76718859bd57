using MyBya.Converters;
using MyBya.Enums;

namespace MyBya.Ui.Views.Components;

public class SportsSelector : Selector
{
	public SportsSelector() : base()
	{
		picker.ItemsSource = new List<SportEnum>
		{
			SportEnum.NONE,
			SportEnum.RUNNING,
            //SportEnum.ROWING,
            //SportEnum.CYCLING,
            //SportEnum.SWIMMING
        };
		
		picker.ItemDisplayBinding = new Binding(".", converter: new EnumDisplayConverter());
	}
}