﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MyBya.Ui.Views.BaseView"
             BackgroundColor="Transparent">
        <Grid
            Margin="0,0,0,0"
            RowDefinitions="Auto, *">
            <Image
                Scale="1.2"
                Margin="0,-6,0,0"
                Source="test_protcol_rouding_header.png"/>
            <ScrollView
                Grid.Row="1"
                Margin="0,0,0,0"
                BackgroundColor="Transparent">
                <Grid
                    RowDefinitions="Auto, Auto, *"
                    Margin="0,10,0,0">
                    <Grid
                        ColumnDefinitions="*">
                        <ImageButton
                            Clicked="navigationIcon_Clicked"
                            Scale="0.7"
                            x:Name="navigationIcon"
                            IsVisible="False"
                            Margin="0,12,0,0"
                            HorizontalOptions="Start"
                            BackgroundColor="Transparent"
                            Source="ic_back.png">
                            <ImageButton.HeightRequest>
                                <OnPlatform x:TypeArguments="x:Double">
                                    <On Platform="iOS" Value="15"/>
                                    <On Platform="Android" Value="40"/>
                                </OnPlatform>
                            </ImageButton.HeightRequest>
                            <ImageButton.WidthRequest>
                                    <OnPlatform x:TypeArguments="x:Double">
                                        <On Platform="iOS" Value="15"/>
                                        <On Platform="Android" Value="40"/>
                                    </OnPlatform>
                            </ImageButton.WidthRequest>
                        </ImageButton>
                         <Label
                            x:Name="title"
                            Margin="0,20,0,10"
                            WidthRequest="300"
                            FontSize="24"
                            TextColor="#FF7FE8"
                            HorizontalTextAlignment="Center"/>
                    </Grid>
                    <Label
                        TextColor="White"
                        Grid.Row="1"
                        x:Name="subTitle"
                        Margin="10,0,10,0"
                        FontSize="18"
                        HorizontalTextAlignment="Start"/>
                    <Grid
                        BackgroundColor="Transparent"
                        RowDefinitions="*"
                        Margin="0,15,0,0"
                        Grid.Row="2"
                        Padding="10,0,10,0"
                        x:Name="mainContent">
                    </Grid>
                </Grid>
            </ScrollView>
        </Grid>
</ContentView>
