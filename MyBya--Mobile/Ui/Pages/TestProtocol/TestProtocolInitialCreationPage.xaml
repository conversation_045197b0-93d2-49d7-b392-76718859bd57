﻿<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.TestProtocol.TestProtocolInitialCreationPage"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels.TestProtocol"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    x:TypeArguments="viewModels:TestProtocolInitialCreationViewModel"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    Title="TestProtocolInitialCreationPage">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            VerticalOptions="Fill"
            TitleText="Creating a Test Protocol"
            SubTitleText="The information we are asking you to provide will be used to create the test protocol, biomarker analysis and training plan recommendations tailored specifically to you.">
            <views:BaseView.MainContent>
                <components:TopRoundedBorderLayoutView
                    TitleText="Training Focus">
                    <Grid
                        RowDefinitions="*">
                         <VerticalStackLayout
                            Spacing="10"
                            Padding="10">
                            <components:SportsSelector
                                SelectedItem="{Binding SelectedSport}"
                                SelectorHorizontalOptions="Fill"
                                SelectorHeightRequest="40"
                                TitleText="Sport *"
                                TitleFontSize="15"/>
                            <components:LevelsSelector
                                IsVisible="{Binding IsSelectedSportNotEmpty}"
                                SelectedItem="{Binding SelectedLevel}"
                                SelectorHorizontalOptions="Fill"
                                SelectorHeightRequest="40"
                                TitleText="Level *"
                                TitleFontSize="15"/>
                            <components:Selector
                                IsVisible="{Binding IsSelectedSportNotEmpty}"
                                SelectedItem="{Binding SelectedEvent}"
                                ItemSource="{Binding EventList}"
                                SelectorHorizontalOptions="Fill"
                                SelectorHeightRequest="40"
                                TitleText="Event *"
                                TitleFontSize="15"/>
                            <Button
                                IsEnabled="{Binding IsNextButtonEnabled}"
                                Style="{StaticResource PrimaryButton}"
                                Grid.Row="1"
                                Margin="0,50,0,0"
                                Text="NEXT"
                                Command="{Binding NextPhaseCommand}" />
                        </VerticalStackLayout>
                    </Grid>
                </components:TopRoundedBorderLayoutView>
            </views:BaseView.MainContent>
        </views:BaseView>
    </Grid>
</local:BaseContentPage>
