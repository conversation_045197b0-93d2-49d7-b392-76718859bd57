﻿<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage 
        xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
        xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
        x:Class="MyBya.Ui.Pages.TestProtocol.BepsScoresPage"
        xmlns:views="clr-namespace:MyBya.Ui.Views"
        xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels.TestProtocol"
        x:TypeArguments="viewModels:BepsScoresViewModel"
        xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
        xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
        xmlns:chart="clr-namespace:Syncfusion.Maui.Toolkit.Charts;assembly=Syncfusion.Maui.Toolkit"
        Title="BepsScoresPage">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            TitleText="My Power Scores"
            SubTitleText="The precise, data-driven insights into energy system utilization enable us to optimize your training to enhance performance and achieve measurable results.">
            <views:BaseView.MainContent>
                <components:TopRoundedBorderLayoutView
                    TitleText="BioEnergetic Power Scores (BEPS)">
                    <VerticalStackLayout
                        Padding="20">
                        <Border
                            Padding="5"
                            Stroke="Black">
                            <chart:SfCartesianChart
                                HeightRequest="300">
                                <chart:SfCartesianChart.XAxes>
                                    <chart:CategoryAxis
                                        LabelPlacement="BetweenTicks"
                                        EdgeLabelsDrawingMode="Center"
                                        LabelsIntersectAction="MultipleRows">
                                        <chart:CategoryAxis.LabelStyle>
                                            <chart:ChartAxisLabelStyle FontSize="7"/>
                                        </chart:CategoryAxis.LabelStyle>
                                    </chart:CategoryAxis>
                                </chart:SfCartesianChart.XAxes>

                                <chart:SfCartesianChart.YAxes>
                                    <chart:NumericalAxis ShowMajorGridLines="True" />
                                </chart:SfCartesianChart.YAxes>

                                <chart:ColumnSeries
                                    ItemsSource="{Binding Items}"
                                    XBindingPath="Name"
                                    YBindingPath="Value"
                                    Fill="#ff2ee0"
                                    Opacity="1" />

                            </chart:SfCartesianChart>
                        </Border>
                        <Grid
                            RowSpacing="20"
                            Margin="0,10,0,0"
                            RowDefinitions="Auto,Auto,Auto"
                            ColumnDefinitions="25,Auto,Auto,Auto, Auto,Auto">
                            <BoxView
                                Color="#FF7FE8"
                                HorizontalOptions="Start"
                                WidthRequest="10"
                                Grid.RowSpan="4"/>
                            <Label
                                Grid.Column="1"
                                Grid.ColumnSpan="4"
                                FontFamily="RobotoBold"
                                HorizontalTextAlignment="Start"
                                Text="{Binding Date}"
                                TextColor="Black"
                                FontAttributes="Bold"
                                FontSize="18"/>
                            <!--!> First row -->
                            <components:LabelValue
                                Grid.Row="1"
                                Grid.Column="1"
                                HorizontalOptions="Center"
                                TitleText="AF"
                                TitleTextColor="#483778"
                                LableValueText="{Binding CurrentBepsTestData.Af}"
                                TitleFontSize="15"
                                ValueFontSize="18"/>
                            <components:LabelValue
                                Grid.Row="1"
                                Grid.Column="2"
                                TitleText="PAC"
                                TitleTextColor="#483778"
                                HorizontalOptions="Center"
                                LableValueText="{Binding CurrentBepsTestData.Pac}"
                                TitleFontSize="15"
                                ValueFontSize="18"/>
                             <components:LabelValue
                                Grid.Row="1"
                                Grid.Column="3"
                                TitleText="LTCC"
                                TitleTextColor="#483778"
                                LableValueText="{Binding CurrentBepsTestData.Ltcc}"
                                TitleFontSize="15"
                                ValueFontSize="18"/>
                            <components:LabelValue
                                Grid.Row="1"
                                Grid.Column="4"
                                TitleText="ARC-3"
                                TitleTextColor="#483778"
                                LableValueText="{Binding CurrentBepsTestData.Arc3}"
                                TitleFontSize="15"
                                ValueFontSize="18"/>
                            <!--!> Second row -->
                            <components:LabelValue
                                Grid.Column="1"
                                Grid.Row="2"
                                TitleText="ARC-2"
                                TitleTextColor="#483778"
                                TitleFontSize="15"
                                ValueFontSize="18"
                                HorizontalOptions="Center"
                                LableValueText="{Binding CurrentBepsTestData.Arc2}"/>
                             <components:LabelValue
                                Margin="20,0,0,0"
                                Grid.Column="2"
                                Grid.Row="2"
                                TitleText="ARC-1"
                                TitleTextColor="#483778"
                                TitleFontSize="15"
                                ValueFontSize="18"
                                LableValueText="{Binding CurrentBepsTestData.Arc1}"/>
                            <components:LabelValue
                                Margin="20,0,0,0"
                                Grid.Row="3"
                                Grid.Column="3"
                                TitleText="ANRC-2"
                                TitleTextColor="#483778"
                                TitleFontSize="15"
                                ValueFontSize="18"
                                LableValueText="{Binding CurrentBepsTestData.Anrc2}"/>
                            <components:LabelValue
                                Margin="20,0,0,0"
                                TitleTextColor="#483778"
                                Grid.Row="2"
                                Grid.Column="4"
                                TitleText="ANRC-1"
                                TitleFontSize="15"
                                ValueFontSize="18"
                                LableValueText="{Binding CurrentBepsTestData.Anrc1}"/>
                        </Grid>
                        <Button
                            Style="{StaticResource SecondaryButton}"
                            Margin="0,20,0,0"
                            Text="HEART RATE AND TRAINING TARGETS"
                            Command="{Binding ContinueCommand}"/>
                    </VerticalStackLayout>
                </components:TopRoundedBorderLayoutView>
                <VerticalStackLayout
                    Spacing="10"
                    Margin="0,20,0,0">
                    <Label
                        FontAttributes="Bold"
                        Text="What's next?"
                        FontSize="22"
                        TextColor="#FF7FE8"/>
                    <Label
                        FontAttributes="Bold"
                        HorizontalTextAlignment="Start"
                        Text="Based on your personalized power scores, we recommend purchasing a training plan to help you enhance your workout routine. Once purchased, this test’s power scores will be automatically added."
                        FontSize="18"
                        TextColor="White"/>
                </VerticalStackLayout>
                <Button
                    Command="{Binding ViewTrainingPlansCommand}"
                    Style="{StaticResource PrimaryButton}"
                    Margin="0,20,0,20"
                    Text="VIEW TRAINING PLANS"/>
            </views:BaseView.MainContent>
        </views:BaseView>
    </Grid>
</local:BaseContentPage>