<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage 
        xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
        xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
        x:Class="MyBya.Ui.Pages.TestProtocol.TestProtocolSetupPage"
        xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
        xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels.TestProtocol"
        x:TypeArguments="viewModels:TestProtocolSetupViewModel"
        xmlns:views="clr-namespace:MyBya.Ui.Views"
        xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
        xmlns:behaviors="clr-namespace:MyBya.Ui.Behaviors"
        Title="TestProtocolSetupPage">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            TitleText="Hi, {name}"
            SubTitleText="We provide comprehensive, data-driven insights to create tailored training regimens that maximize efficiency, improve outcomes and transform athletic potential.">
            <views:BaseView.MainContent>
                <Grid
                    RowDefinitions="*, *">
                    <VerticalStackLayout
                        Spacing="10"
                        Margin="0,20,0,0">
                        <components:Selector
                            SelectorHorizontalOptions="Start"
                            SelectorWidthRequest="250"
                            SelectedItem="{Binding SelectedAthlete}"
                            ItemSource="{Binding Athletes}"
                            TitleFontSize="16"
                            TitleTextColor="White"
                            TitleText="Select Athlete"
                            SelectorBackgroundColor="White"/>
                        <components:Selector
                            SelectorHorizontalOptions="Start"
                            SelectorWidthRequest="250"
                            SelectedItem="{Binding SelectedSport}"
                            ItemSource="{Binding Sports}"
                            ItemDisplayBinding="{Binding ., Converter={StaticResource enumDisplayConverter}}"
                            TitleFontSize="16"
                            TitleTextColor="White"
                            TitleText="Sports"
                            SelectorBackgroundColor="White"/>
                        <components:Selector
                            SelectorHorizontalOptions="Start"
                            SelectorWidthRequest="250"
                            SelectedItem="{Binding SelectedLevel}"
                            ItemDisplayBinding="{Binding ., Converter={StaticResource enumDisplayConverter}}"
                            ItemSource="{Binding Levels}"
                            TitleFontSize="16"
                            TitleTextColor="White"
                            TitleText="Level"
                            SelectorBackgroundColor="White"/>
                        <VerticalStackLayout>
                            <Label
                                Text="Run Pace"
                                FontSize="16"
                                TextColor="White"/>
                            <Entry
                                HorizontalOptions="Start"
                                WidthRequest="250"
                                BackgroundColor="White"
                                Text="{Binding RunPace, Mode=TwoWay}"
                                Placeholder="00:00"
                                Keyboard="Numeric">
                                <Entry.Behaviors>
                                    <behaviors:TimeFormattingBehavior />
                                </Entry.Behaviors>
                            </Entry>
                        </VerticalStackLayout>
                        <components:Selector
                            SelectorHorizontalOptions="Start"
                            SelectorWidthRequest="250"
                            SelectedItem="{Binding SelectedTrackOrTreadmill}"
                            ItemSource="{Binding TrackOrTreadmillOptions}"
                            ItemDisplayBinding="{Binding ., Converter={StaticResource enumDisplayConverter}}"
                            TitleFontSize="16"
                            TitleTextColor="White"
                            TitleText="Track/Treadmill"
                            SelectorBackgroundColor="White"/>
                        <Button
                             Style="{StaticResource DefaultButton}"
                             Grid.Row="1"
                             VerticalOptions="End"
                             Margin="0,20,0,10"
                             CornerRadius="12"
                             Text="CONTINUE"
                             Command="{Binding ContinueCommand}"/>
                    </VerticalStackLayout>
                </Grid>
            </views:BaseView.MainContent>
        </views:BaseView>
       
    </Grid>
</local:BaseContentPage>