<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage 
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels.TestProtocol"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    x:Class="MyBya.Ui.Pages.TestProtocol.YourTestProtocolPage"
    x:TypeArguments="viewModels:YourTestProtocolViewModel"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    Title="{Binding Title}">
    <Grid 
        RowDefinitions="*">
            <views:BaseView 
                TitleText="Your Test Protocol"
                SubTitleText="The following is the test protocol specifically created for you.">
                <views:BaseView.MainContent>
                    <components:TopRoundedBorderLayoutView
                        TitleText="Testing Inputs">
                        <VerticalStackLayout
                            Padding="10"
                            Spacing="10">
                            <components:LabelValue
                                TitleTextColor="Black"
                                TitleFontAttributes="Bold"
                                TitleFontSize="15"
                                TitleText="Sport"
                                ValueFontSize="16"
                                LableValueText="{Binding SportAndEvent}"/>
                            <components:LabelValue
                                TitleFontSize="15"
                                TitleFontAttributes="Bold"
                                TitleText="Easy Pace"
                                ValueFontSize="16"
                                LableValueText="{Binding RaceGoal}"/>
                            <components:LabelValue
                                TitleFontSize="15"
                                TitleFontAttributes="Bold"
                                TitleText="Test Location"
                                ValueFontSize="16"
                                LableValueText="{Binding TestLocation}"/>
                            <components:LabelValue
                                TitleFontSize="15"
                                TitleFontAttributes="Bold"
                                TitleText="Data Capture"
                                ValueFontSize="16"
                                LableValueText="{Binding DataCapture}"/>
                            <Button
                                Style="{StaticResource SecondaryButton}"
                                Text="EDIT TESTING INPUT"
                                Command="{Binding EditTestingInputCommand}"/>
                        </VerticalStackLayout>
                    </components:TopRoundedBorderLayoutView>
                    <components:TopRoundedBorderLayoutView
                        Margin="0,10,0,0"
                        TitleText="Testing Protocol">
                        <VerticalStackLayout
                            Padding="10,20,20,20"
                            Spacing="10">
                            <Grid
                                RowSpacing="35"
                                Margin="0,0,0,0"
                                RowDefinitions="Auto, Auto, Auto, Auto, Auto, Auto"
                                ColumnDefinitions="100, *, *">
                                <Label
                                    FontFamily="RobotoBold"
                                    Text="Distance"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    TextColor="Black"/>
                                <Label
                                    HorizontalTextAlignment="Start"
                                    VerticalTextAlignment="Center"
                                    Grid.Column="1"
                                    Grid.ColumnSpan="2"
                                    Text="{Binding TestProtocolInstruction.DistanceTimeInstructions}"
                                    FontSize="16"
                                    Margin="20,0,0,0"
                                    TextColor="#0B0B0B"/>
                                <Label
                                    FontFamily="RobotoBold"
                                    Grid.Row="1"
                                    Text="Stages"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    TextColor="#0B0B0B"/>
                                <Label
                                    HorizontalTextAlignment="Start"
                                    VerticalTextAlignment="Center"
                                    Grid.Row="1"
                                    Grid.Column="1"
                                    Margin="20,0,0,0"
                                    Text="{Binding TestProtocolInstruction.StagesInstructions}"
                                    FontSize="16"
                                    Grid.ColumnSpan="2"
                                    TextColor="Black"/>
                                <Label
                                    FontFamily="RobotoBold"
                                    Grid.Row="2"
                                    Text="Start"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    TextColor="Black"/>
                                <Label
                                    HorizontalTextAlignment="Start"
                                    Grid.Row="2"
                                    Grid.Column="1"
                                    Grid.ColumnSpan="2"
                                    Margin="20,0,0,0"
                                    Text="{Binding TestProtocolInstruction.StartInstructions}"
                                    FontSize="16"
                                    TextColor="Black"/>
                                <Label
                                    FontFamily="RobotoBold"
                                    Grid.Row="3"
                                    Text="Increase Effort Gradually"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    TextColor="Black"/>
                                <Label
                                    HorizontalTextAlignment="Start"
                                    Grid.Row="3"
                                    Grid.Column="1"
                                    Grid.ColumnSpan="2"
                                    Text="{Binding TestProtocolInstruction.EffortInstructions}"
                                    FontSize="16"
                                    Margin="20,0,0,0"
                                    TextColor="Black"/>
                                <Label
                                    FontFamily="RobotoBold"
                                    Grid.Row="4"
                                    Text="Finish with Max Effort"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    TextColor="Black"/>
                                <Label
                                    Grid.Row="4"
                                    HorizontalTextAlignment="Start"
                                    Grid.Column="1"
                                    Grid.ColumnSpan="2"
                                    Text="{Binding TestProtocolInstruction.FinishInstructions}"
                                    FontSize="16"
                                    Margin="20,0,0,0"
                                    TextColor="Black"/>
                                <Label
                                    FontFamily="RobotoBold"
                                    Grid.Row="5"
                                    Text="Record"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    TextColor="Black"/>
                                <Label
                                    HorizontalTextAlignment="Start"
                                    Grid.Row="5"
                                    Grid.Column="1"
                                    Grid.ColumnSpan="2"
                                    Text="{Binding TestProtocolInstruction.RecordInstructions}"
                                    FontSize="16"
                                    Margin="20,0,0,0"
                                    TextColor="Black"/>
                            </Grid>
                        </VerticalStackLayout>
                    </components:TopRoundedBorderLayoutView>
                    <components:TopRoundedBorderLayoutView
                        Margin="0,10,0,0"
                        TitleText="Now What?"/>
                        <Frame
                            BorderColor="Transparent"
                            Margin="0,-10,0,0"
                            BackgroundColor="White">
                            <Grid
                                RowDefinitions="Auto, Auto, Auto, Auto"
                                ColumnDefinitions="*"
                                Padding="10"
                                RowSpacing="10">
                                <!-- <toolkit:MediaElement
                                    HeightRequest="200"
                                    WidthRequest="320"
                                    ShouldAutoPlay="False"
                                    ShouldShowPlaybackControls="True"
                                    Source="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"/>
                                <Label
                                    Grid.Row="1"
                                    FontAttributes="Bold"
                                    Text="Step-by-step Guide to Taking Your Performance Test"
                                    FontSize="16"
                                    TextColor="Black"/> -->
                                <Label
                                    Margin="0,0,0,0"
                                    Text="Now begin your workout and get ready to record your test workout data. Click on the button below when you are ready to start adding your test data"
                                    FontSize="16"/>
                                <Button
                                    Grid.Row="3"
                                    Style="{StaticResource PrimaryButton}"
                                    Text="ENTER TEST WORKOUT DATA"
                                    Command="{Binding NavigateToEnterTestDataCommand}"/>
                            </Grid>
                        </Frame>
                </views:BaseView.MainContent>
            </views:BaseView>
    </Grid>
</local:BaseContentPage>
