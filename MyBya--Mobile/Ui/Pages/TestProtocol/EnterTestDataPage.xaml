<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage 
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels.TestProtocol"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    x:Class="MyBya.Ui.Pages.TestProtocol.EnterTestDataPage"
    x:TypeArguments="viewModels:EnterTestDataViewModel"
    xmlns:behaviors="clr-namespace:MyBya.Ui.Behaviors"
    Title="EnterTestDataPage">
    <Grid RowDefinitions="Auto, *">
        <views:BaseView 
            HasNavigationBack="True"
            TitleText="Enter Test Data"
            SubTitleText="Fill out the data from your test in the following form."/>
        <Grid BackgroundColor="#483778"
              RowDefinitions="*"
              Grid.Row="1">
            <Grid.Background>
                <LinearGradientBrush>
                    <GradientStop Color="#483778" Offset="0.44"/>
                    <GradientStop Color="#231B3B" Offset="1.0"/>
                </LinearGradientBrush>
            </Grid.Background>
            <Grid 
                Margin="10"
                ColumnDefinitions="*"
                RowDefinitions="*, Auto">
                <Border 
                    Stroke="Transparent"
                    BackgroundColor="White">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="5"/>
                    </Border.StrokeShape>
                    <Grid 
                        RowDefinitions="Auto, *, Auto">
                        <Image 
                            Scale="1.2"
                            Source="enter_test_data_rouding_header.png"/>
                        <ScrollView
                             Grid.Row="1">
                            <VerticalStackLayout 
                                Padding="10">
                                <Label 
                                    Text="Workout Inputs"
                                    FontSize="20"
                                    TextColor="#FF7FE8"/>
                                <Grid 
                                    ColumnSpacing="10"
                                    ColumnDefinitions="*, *, *, *, *"
                                    RowDefinitions="Auto, Auto, Auto"
                                    Margin="0,10,0,0">
                                    <Label 
                                        Text="{Binding StandardInterval}">
                                        <Label.FontSize>
                                            <OnPlatform x:TypeArguments="x:Double">
                                                <On Platform="iOS" Value="15"/>
                                                <On Platform="Android" Value="15"/>
                                            </OnPlatform>
                                        </Label.FontSize>    
                                    </Label>
                                    <Label 
                                        Grid.Column="1"
                                        Text="Actual Effort">
                                         <Label.FontSize>
                                            <OnPlatform x:TypeArguments="x:Double">
                                                <On Platform="iOS" Value="15"/>
                                                <On Platform="Android" Value="15"/>
                                            </OnPlatform>
                                        </Label.FontSize>    
                                    </Label>
                                    <Label 
                                        Grid.Column="2"
                                        Margin="0,0,10,0"
                                        HorizontalTextAlignment="Center"
                                        Text="{Binding IntervalTypeTitle}">
                                         <Label.FontSize>
                                            <OnPlatform x:TypeArguments="x:Double">
                                                <On Platform="iOS" Value="15"/>
                                                <On Platform="Android" Value="15"/>
                                            </OnPlatform>
                                        </Label.FontSize>    
                                    </Label>
                                    <Label
                                        Grid.Column="3"
                                        Margin="0,0,10,0"
                                        HorizontalTextAlignment="Center">
                                         <Label.FontSize>
                                            <OnPlatform x:TypeArguments="x:Double">
                                                <On Platform="iOS" Value="15"/>
                                                <On Platform="Android" Value="15"/>
                                            </OnPlatform>
                                        </Label.FontSize>    
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <Span 
                                                    Text="HR"/>
                                                <Span
                                                    Text="&#10;(BPM)"
                                                    TextColor="#ABABAB">
                                                     <Span.FontSize>
                                                        <OnPlatform x:TypeArguments="x:Double">
                                                            <On Platform="iOS" Value="14"/>
                                                            <On Platform="Android" Value="14"/>
                                                        </OnPlatform>
                                                    </Span.FontSize>
                                                </Span>
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                    <Label 
                                        HorizontalTextAlignment="Center"
                                        IsVisible="{Binding IsBlacVisible}"
                                        Grid.Column="4">
                                         <Label.FontSize>
                                            <OnPlatform x:TypeArguments="x:Double">
                                                <On Platform="iOS" Value="15"/>
                                                <On Platform="Android" Value="15"/>
                                            </OnPlatform>
                                        </Label.FontSize>    
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <Span Text="BLac"/>
                                                <Span
                                                    Text="&#10;(mmol/L)"
                                                    TextColor="#ABABAB">
                                                    <Span.FontSize>
                                                        <OnPlatform x:TypeArguments="x:Double">
                                                            <On Platform="iOS" Value="14"/>
                                                            <On Platform="Android" Value="14"/>
                                                        </OnPlatform>
                                                    </Span.FontSize>
                                                </Span>
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                </Grid>
                                <Grid
                                    ColumnDefinitions="*, *, *, *, *">
                                    <VerticalStackLayout
                                        HeightRequest="450"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Start"
                                        IsVisible="{Binding IsEightIntervalsVisible}">
                                        <VerticalStackLayout.Spacing>
                                            <OnPlatform x:TypeArguments="x:Double">
                                                <On Platform="iOS" Value="29"/>
                                                <On Platform="Android" Value="31"/>
                                            </OnPlatform>
                                        </VerticalStackLayout.Spacing>
                                        <VerticalStackLayout.Scale>
                                            <OnPlatform x:TypeArguments="x:Double">
                                                <On Platform="iOS" Value="1.040"/>
                                                <On Platform="Android" Value="1.030"/>
                                            </OnPlatform>
                                        </VerticalStackLayout.Scale>
                                        <VerticalStackLayout.Margin>
                                            <OnPlatform x:TypeArguments="Thickness">
                                                <On Platform="iOS" Value="0,40,0,0"/>
                                                <On Platform="Android" Value="0,42,20,0"/>
                                            </OnPlatform>
                                        </VerticalStackLayout.Margin>
                                        <Label
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="1"/>
                                        <Label
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="2"/>
                                        <Label
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="3"/>
                                        <Label
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="4"/>
                                        <Label
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="5"/>
                                        <Label  
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="6"/>
                                        <Label
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="7"/>
                                        <Label
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="8"/>
                                    </VerticalStackLayout>
                                    <VerticalStackLayout
                                        HorizontalOptions="Center"
                                        VerticalOptions="Start"
                                        HeightRequest="300"
                                        IsVisible="{Binding IsFourIntervalsVisible}"
                                        Grid.Column="0">
                                         <VerticalStackLayout.Spacing>
                                            <OnPlatform x:TypeArguments="x:Double">
                                                <On Platform="iOS" Value="29"/>
                                                <On Platform="Android" Value="30"/>
                                            </OnPlatform>
                                        </VerticalStackLayout.Spacing>
                                        <VerticalStackLayout.Scale>
                                            <OnPlatform x:TypeArguments="x:Double">
                                                <On Platform="iOS" Value="1.040"/>
                                                <On Platform="Android" Value="1.030"/>
                                            </OnPlatform>
                                        </VerticalStackLayout.Scale>
                                        <VerticalStackLayout.Margin>
                                            <OnPlatform x:TypeArguments="Thickness">
                                                <On Platform="iOS" Value="0,40,0,0"/>
                                                <On Platform="Android" Value="0,42,20,0"/>
                                            </OnPlatform>
                                        </VerticalStackLayout.Margin>
                                        <Label
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="1"/>
                                        <Label
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="2"/>
                                        <Label
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="3"/>
                                        <Label
                                            FontSize="17"
                                            HorizontalTextAlignment="Center"
                                            Text="4"/>
                                    </VerticalStackLayout>
                                    <VerticalStackLayout
                                        HeightRequest="450"
                                        HorizontalOptions="Start"
                                        IsVisible="{Binding IsFourIntervalsVisible}"
                                        Grid.Column="1">
                                         <VerticalStackLayout.Scale>
                                            <OnPlatform x:TypeArguments="x:Double">
                                                <On Platform="iOS" Value="1.050"/>
                                                <On Platform="Android" Value="1.095"/>
                                            </OnPlatform>
                                        </VerticalStackLayout.Scale>
                                        <VerticalStackLayout.Margin>
                                            <OnPlatform x:TypeArguments="Thickness">
                                                <On Platform="iOS" Value="10,40,0,0"/>
                                                <On Platform="Android" Value="10,54,0,0"/>
                                            </OnPlatform>
                                        </VerticalStackLayout.Margin>
                                        <Label 
                                            HorizontalTextAlignment="Center"
                                            Text="min"/>
                                        <Image 
                                            Source="ic_four_intervals_graph.png"/>
                                        <Label
                                            HorizontalTextAlignment="Center"
                                            Text="max"/>
                                    </VerticalStackLayout>
                                    <VerticalStackLayout
                                        IsVisible="{Binding IsEightIntervalsVisible}"
                                        HorizontalOptions="Start"
                                        HeightRequest="450"
                                        Grid.Column="1">
                                        <VerticalStackLayout.Scale>
                                            <OnPlatform x:TypeArguments="x:Double">
                                                <On Platform="iOS" Value="1.050"/>
                                                <On Platform="Android" Value="1.095"/>
                                            </OnPlatform>
                                        </VerticalStackLayout.Scale>
                                        <VerticalStackLayout.Margin>
                                            <OnPlatform x:TypeArguments="Thickness">
                                                <On Platform="iOS" Value="10,43,0,0"/>
                                                <On Platform="Android" Value="10,54,0,0"/>
                                            </OnPlatform>
                                        </VerticalStackLayout.Margin>
                                        <Label 
                                            HorizontalTextAlignment="Center"
                                            Text="min"/>
                                        <Image 
                                            Source="ic_eight_intervals_graph.png"/>
                                        <Label
                                            HorizontalTextAlignment="Center"
                                            Text="max"/>
                                    </VerticalStackLayout>
                                    <VerticalStackLayout
                                        VerticalOptions="Start"
                                        Grid.Column="2"
                                        Grid.ColumnSpan="3">
                                        <VerticalStackLayout.Margin>
                                            <OnPlatform x:TypeArguments="Thickness">
                                                <On Platform="iOS" Value="0,20,0,0"/>
                                                <On Platform="Android" Value="0,0,0,0"/>
                                            </OnPlatform>
                                        </VerticalStackLayout.Margin>
                                        <CollectionView
                                            Margin="0,10,0,0"
                                            MaximumHeightRequest="500"
                                            ItemsSource="{Binding Items}">
                                                <CollectionView.ItemTemplate>
                                                    <DataTemplate>
                                                        <Grid 
                                                            ColumnSpacing="10"
                                                            RowDefinitions="Auto"
                                                            ColumnDefinitions="*, *, *">
                                                            <Grid.Margin>
                                                                <OnPlatform x:TypeArguments="Thickness">
                                                                    <On Platform="iOS" Value="0,19,0,0"/>
                                                                    <On Platform="Android" Value="0,18,0,0"/>
                                                                </OnPlatform>
                                                            </Grid.Margin>
                                                            <components:CEntry
                                                                Text="{Binding Time}"
                                                                HorizontalTextAlignment="Center"
                                                                VerticalTextAlignment="Center"
                                                                BorderColor="Black"
                                                                WidthRequest="55"
                                                                Placeholder="00:00"
                                                                IsVisible="{Binding MinutesAndSecondsInputsVisible}"
                                                                Keyboard="Numeric">
                                                                <components:CEntry.HeightRequest>
                                                                    <OnPlatform x:TypeArguments="x:Double">
                                                                        <On Platform="iOS" Value="35"/>
                                                                        <On Platform="Android" Value="38"/>
                                                                    </OnPlatform>
                                                                </components:CEntry.HeightRequest>
                                                                <Entry.Behaviors>
                                                                    <behaviors:TimeFormattingBehavior />
                                                                </Entry.Behaviors>
                                                            </components:CEntry>
                                                            <components:CEntry 
                                                                HorizontalOptions="Start"
                                                                IsVisible="{Binding MPHInputVisible}"
                                                                Keyboard="Numeric"
                                                                WidthRequest="55"
                                                                BorderColor="Black"
                                                                VerticalTextAlignment="Center"
                                                                HorizontalTextAlignment="Center"
                                                                Placeholder="0"
                                                                Text="{Binding ActualPaceMph}">
                                                                <components:CEntry.HeightRequest>
                                                                    <OnPlatform x:TypeArguments="x:Double">
                                                                        <On Platform="iOS" Value="35"/>
                                                                        <On Platform="Android" Value="38"/>
                                                                    </OnPlatform>
                                                                </components:CEntry.HeightRequest>
                                                            </components:CEntry>
                                                            
                                                            <components:CEntry 
                                                                Grid.Column="1"
                                                                Keyboard="Numeric"
                                                                WidthRequest="55"
                                                                BorderColor="Black"
                                                                HorizontalTextAlignment="Center"
                                                                VerticalTextAlignment="Center"
                                                                Placeholder="0"
                                                                Text="{Binding HeartRate}">
                                                                <components:CEntry.HeightRequest>
                                                                    <OnPlatform x:TypeArguments="x:Double">
                                                                        <On Platform="iOS" Value="35"/>
                                                                        <On Platform="Android" Value="38"/>
                                                                    </OnPlatform>
                                                                </components:CEntry.HeightRequest>
                                                            </components:CEntry>
                                                            
                                                            <components:CEntry 
                                                                IsVisible="{Binding IsBlacVisible}"
                                                                Grid.Column="2"
                                                                Keyboard="Numeric"
                                                                WidthRequest="55"
                                                                BorderColor="Black"
                                                                HorizontalTextAlignment="Center"
                                                                VerticalTextAlignment="Center"
                                                                Placeholder="0"
                                                                Text="{Binding BLacValue}">
                                                                <components:CEntry.HeightRequest>
                                                                    <OnPlatform x:TypeArguments="x:Double">
                                                                        <On Platform="iOS" Value="35"/>
                                                                        <On Platform="Android" Value="38"/>
                                                                    </OnPlatform>
                                                                </components:CEntry.HeightRequest>
                                                            </components:CEntry>
                                                        </Grid>
                                                    </DataTemplate>
                                            </CollectionView.ItemTemplate>
                                        </CollectionView>
                                        <Button 
                                            Margin="0,10,0,0"
                                            IsVisible="{Binding IsAddNextIntervalVisible}"
                                            Command="{Binding AddNextIntervalCommand}"
                                            MinimumWidthRequest="150"
                                            WidthRequest="190"
                                            BorderColor="#483778"
                                            BackgroundColor="White"
                                            BorderWidth="1"
                                            CornerRadius="8"
                                            TextColor="#483778"
                                            HorizontalOptions="Start"
                                            Text="ADD NEXT STAGE"/>
                                    </VerticalStackLayout>
                                </Grid>
                                <Grid
                                    ColumnDefinitions="*, *">
                                    <Button
                                        Grid.Column="0"
                                        Margin="0,0,0,5"
                                        Style="{StaticResource SecondaryButton}"
                                        HeightRequest="40"
                                        WidthRequest="150"
                                        Command="{Binding NavigateBackCommand}"
                                        IsVisible="{Binding IsSecondarySubmitButtonVisible}"
                                        Text="BACK"/>
                                    <Button 
                                        Grid.Column="1"
                                        Margin="0,0,0,5"
                                        IsEnabled="{Binding IsSubmitButtonEnabled}"
                                        Style="{StaticResource PrimaryButton}"
                                        HeightRequest="40"
                                        WidthRequest="150"
                                        Command="{Binding SubmitCommand}"
                                        IsVisible="{Binding IsSecondarySubmitButtonVisible}"
                                        Text="SUBMIT"/>
                                </Grid>
                            </VerticalStackLayout>
                        </ScrollView>
                        <Grid
                            Padding="5"
                            Grid.Row="2"
                            ColumnDefinitions="*, *">
                            <Button
                                Margin="0,0,5,0"
                                Text="BACK"
                                Grid.Column="0"
                                Style="{StaticResource SecondaryButton}"
                                HeightRequest="40"
                                Command="{Binding NavigateBackCommand}"
                                IsVisible="{Binding IsSubmitButtonVisible}"/>
                            <Button 
                                Margin="0,0,5,0"
                                Grid.Column="1"
                                IsEnabled="{Binding IsSubmitButtonEnabled}"
                                Style="{StaticResource PrimaryButton}"
                                HeightRequest="40"
                                Command="{Binding SubmitCommand}"
                                Text="SUBMIT"
                                IsVisible="{Binding IsSubmitButtonVisible}"/>
                        </Grid>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
    </Grid>
</local:BaseContentPage>