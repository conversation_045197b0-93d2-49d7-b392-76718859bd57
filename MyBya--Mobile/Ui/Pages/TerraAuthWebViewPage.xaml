<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
        xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
        xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
        x:Class="MyBya.Ui.Pages.TerraAuthWebViewPage"
        Title="TerraAuthWebViewPage"
        xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
        xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels"
        xmlns:views="clr-namespace:MyBya.Ui.Views"
        x:TypeArguments="viewModels:TerraAuthWebViewModel">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            HasNavigationBack="True"
            TitleText="Terra Authorization">
            <views:BaseView.MainContent>
                <Grid RowDefinitions="*" ColumnDefinitions="*">
                    <WebView
                        Grid.Row="0"
                        x:Name="terraAuthWebView"
                        Source="{Binding TerraAuthUrl}"
                        Navigated="terraAuthWebView_Navigated"
                        Navigating="terraAuthWebView_Navigating"
                        HeightRequest="550"
                        WidthRequest="380"/>
                </Grid>
            </views:BaseView.MainContent>
        </views:BaseView>
    </Grid>
</local:BaseContentPage>
