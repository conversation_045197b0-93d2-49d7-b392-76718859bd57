<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.PreviewIndividualPlanPage"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    x:TypeArguments="viewModels:PreviewIndividualPlanViewModel"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    Title="PreviewIndividualPlanPage">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            HasNavigationBack="True"
            TitleText="{Binding TitleText}">
            <views:BaseView.MainContent>
                <VerticalStackLayout>
                    <Grid
                        IsVisible="{Binding ShowPriceInfo}"
                        ColumnSpacing="20"
                        ColumnDefinitions="Auto, *">
                        <Grid
                            Rotation="-10">
                            <Image
                                WidthRequest="65"
                                HeightRequest="65"
                                Source="ic_star.png"/>
                            <VerticalStackLayout
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <Label
                                    FontFamily="RobotoBold"
                                    HorizontalTextAlignment="Center"
                                    VerticalTextAlignment="Center"
                                    FontSize="19"
                                    TextColor="White"
                                    FontAttributes="Bold"
                                    Text="20%"/>
                                <Label
                                    FontFamily="RobotoBold"
                                    HorizontalTextAlignment="Center"
                                    VerticalTextAlignment="Center"
                                    FontSize="13"
                                    TextColor="White"
                                    FontAttributes="Bold"
                                    Text="OFF"/>
                            </VerticalStackLayout>
                        </Grid>
                        <Label
                            HorizontalTextAlignment="Start"
                            Grid.Column="1"
                            TextColor="White"
                            FontSize="17"
                            Text="Your [Subscription level name] comes with a discount on any 12 week training plan (applied at checkout.)"/>
                    </Grid>
                    <components:TopRoundedBorderLayoutView
                        Margin="0,25,0,0">
                        <VerticalStackLayout
                            Padding="20,0,20,20"
                            Spacing="16">
                            <Label
                                Text="{Binding EndDateText}"
                                FontSize="18"
                                TextColor="Black"
                                FontAttributes="Bold"
                                HorizontalTextAlignment="Center"/>
                            <components:LabelValue
                                IsVisible="{Binding ShowPriceInfo}"
                                TitleText="Price"
                                ValueFontSize="20"
                                LableValueText="$8.00"/>
                            <Label
                                Text="{Binding DescriptionText}"
                                FontSize="18"
                                TextColor="Black"
                                LineHeight="1.5" />
                            <Button
                                IsVisible="{Binding ShowPriceInfo}"
                                Command="{Binding BuyPlanCommand}"
                                WidthRequest="175"
                                HeightRequest="45"
                                HorizontalOptions="Center"
                                Style="{StaticResource PrimaryButton}"
                                Text="BUY PLAN" />
                        </VerticalStackLayout>
                    </components:TopRoundedBorderLayoutView>
                </VerticalStackLayout>
            </views:BaseView.MainContent>
        </views:BaseView>
    </Grid>
</local:BaseContentPage >
