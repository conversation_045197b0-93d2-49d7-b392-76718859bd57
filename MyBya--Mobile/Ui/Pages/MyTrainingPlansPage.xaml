<?xml version="1.0" encoding="utf-8" ?>
<pages:BaseContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.MyTrainingPlansPage"
    xmlns:pages="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    xmlns:viewmodels="clr-namespace:MyBya.Ui.ViewModels"
    x:TypeArguments="viewmodels:MyTrainingPlansViewModel"
    Title="MyTrainingPlansPage">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            HasNavigationBack="True"
            TitleText="My Training Plans">
            <views:BaseView.MainContent>
                <VerticalStackLayout
                    Spacing="20"
                    Margin="5,5,5,35"
                    IsVisible="{Binding IsEmptyView}">
                     <Label
                        FontSize="20"
                        Text="You have not purchased any training plans yet."
                        TextColor="White" />
                    <Label
                        FontSize="20"
                        Text="Take a test to have training plans recommended to you."
                        TextColor="White" />
                </VerticalStackLayout>
                <VerticalStackLayout
                    Spacing="10">
                    <Label
                        HorizontalTextAlignment="Center"
                        Text="These are your active training plans:"
                        FontSize="20"
                        TextColor="White" />
                    <CollectionView
                        ItemsSource="{Binding Items}">
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <StackLayout>
                                     <Label
                                        Text="{Binding SportName}"
                                        FontSize="20"
                                        TextColor="White"
                                        FontFamily="RobotoBold">
                                        <Label.Margin>
                                            <OnPlatform x:TypeArguments="Thickness">
                                                <On Platform="iOS" Value="0,20,0,10"/>
                                                <On Platform="Android" Value="0,0,0,0"/>
                                            </OnPlatform>
                                        </Label.Margin>
                                    </Label>
                                    <CollectionView
                                        ItemsSource="{Binding Plans}">
                                        <CollectionView.ItemTemplate>
                                            <DataTemplate>
                                                <StackLayout>
                                                    <Border
                                                        StrokeShape="RoundRectangle 12"
                                                        StrokeThickness="1"
                                                        Margin="0,15,0,0"
                                                        Padding="16"
                                                        BackgroundColor="White">
                                                        <VerticalStackLayout
                                                            Spacing="12">
                                                            <Label
                                                                HorizontalTextAlignment="Start"
                                                                FontAttributes="Bold"
                                                                FontFamily="RobotoBold"
                                                                Text="{Binding Name}"
                                                                FontSize="18"
                                                                TextColor="#D96CC6" />
                                                            <Label
                                                                HorizontalTextAlignment="Start"
                                                                Text="{Binding Description}"
                                                                FontSize="17"
                                                                TextColor="Black" />
                                                            <Button
                                                                Text="DETAILS"
                                                                Style="{StaticResource PrimaryButton}"
                                                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:MyTrainingPlansViewModel}}, Path=PlanDetailsCommand}"
                                                                CommandParameter="{Binding .}"
                                                                HorizontalOptions="Center"
                                                                WidthRequest="150"/>
                                                        </VerticalStackLayout>
                                                    </Border>
                                                </StackLayout>
                                            </DataTemplate>
                                        </CollectionView.ItemTemplate>
                                    </CollectionView>
                                </StackLayout>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </VerticalStackLayout>
                <Button
                    Margin="0,30,0,20"
                    Command="{Binding AddTrainingPlanCommand}"
                    FontSize="18"
                    Text="ADD A TRAINING PLAN"
                    Style="{StaticResource ProfileButtonStyle}"/>
            </views:BaseView.MainContent>
        </views:BaseView>
    </Grid>
</pages:BaseContentPage >
