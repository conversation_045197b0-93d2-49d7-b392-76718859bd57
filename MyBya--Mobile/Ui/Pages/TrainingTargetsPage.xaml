<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.TrainingTargetsPage"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels"
    x:TypeArguments="viewModels:TrainingTargetsViewModel"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    Title="TrainingTargetsPage">
    <Grid
        RowDefinitions="Auto, *">
        <views:BaseView
            VerticalOptions="Fill"
            HasNavigationBack="True"
            TitleText="Heart Rate and Training Targets">
        <views:BaseView.MainContent/>
        </views:BaseView>
        <ScrollView
            BackgroundColor="#483778"
            Grid.Row="1"
            VerticalOptions="Fill">
            <components:TopRoundedBorderLayoutView
                Margin="10"
                Grid.Row="1"
                TitleText="Heart Rate Zones">
                <BoxView 
                    Margin="10"
                    Color="Black"
                    HeightRequest="1"/>
                <Grid
                    Padding="10" 
                    ColumnDefinitions="*,*,*,*"
                    RowSpacing="10"
                    ColumnSpacing="50">
                    <Grid.RowDefinitions>
                        <!-- Zone 1 -->
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="1"/>   <!-- Divider -->
                        <!-- Zone 2 -->
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="1"/>   <!-- Divider -->
                        <!-- Zone 3 -->
                        <RowDefinition Height="Auto"/> 
                    </Grid.RowDefinitions>
                    <!-- Zone 1 -->
                    <Label 
                        Text="Zone 1"
                        Grid.Row="0"
                        Grid.Column="0"/>
                    <Label 
                        Text="&lt;" 
                        Grid.Row="0" 
                        Grid.Column="2"
                        Margin="0,0,0,12"/>
                    <Label 
                        Text="{Binding HeartRates.Zone1}" 
                        Grid.Row="0" 
                        Grid.Column="3"
                        Margin="0,0,0,12"/>
                    <!-- Divider -->
                    <BoxView 
                        Color="Black"
                        HeightRequest="1"
                        Grid.Row="1"
                        Grid.ColumnSpan="4"/>
                    <!-- Zone 2 Low -->
                    <Label 
                        Text="Low"
                        Grid.Row="2"
                        Grid.Column="1"/>
                    <Label 
                        Text="{Binding HeartRates.Zone2LowLeft}" 
                        Grid.Row="2" 
                        Grid.Column="2"/>
                    <Label 
                        Text="{Binding HeartRates.Zone2LowRight}" 
                        Grid.Row="2" 
                        Grid.Column="3"/>
                    <!-- Zone 2 Mid -->
                    <Label
                        VerticalTextAlignment="Center"
                        Text="Zone 2" 
                        Grid.Row="3" 
                        Grid.Column="0"/>
                    <Label
                        VerticalTextAlignment="Center"
                        Text="Mid" 
                        Grid.Row="3" 
                        Grid.Column="1"/>
                    <!-- <Label
                        Text="Mid" 
                        Grid.Row="3" 
                        Grid.Column="1"/> -->
                    <Label 
                        Text="{Binding HeartRates.Zone2MidLeft}" 
                        Grid.Row="3" 
                        Grid.Column="2"/>
                    <Label 
                        Text="{Binding HeartRates.Zone2MidRight}" 
                        Grid.Row="3" 
                        Grid.Column="3"/>
                        <!-- Zone 2 Max -->
                    <Label
                        VerticalTextAlignment="Center"
                        Text="Max" 
                        Grid.Row="4" 
                        Grid.Column="1"/>
                    <Label 
                        Text="{Binding HeartRates.Zone2HighLeft}" 
                        Grid.Row="4" 
                        Grid.Column="2"/>
                    <Label 
                        Text="{Binding HeartRates.Zone2HighRight}" 
                        Grid.Row="4" 
                        Grid.Column="3"/>
                    <!-- Divider -->
                    <BoxView 
                        Color="Black"
                        HeightRequest="1"
                        Grid.Row="5"
                        Grid.ColumnSpan="4"/>
                    <!-- Zone 3 -->
                    <Label
                        Margin="0,10,0,0"
                        VerticalTextAlignment="Center"
                        Text="Zone 3"
                        Grid.Row="6"/>
                    <Label
                        Margin="0,10,0,0"
                        Text="{Binding HeartRates.Zone3}"
                        Grid.Row="6"
                        Grid.Column="1"/>
                    <Label 
                        Margin="0,10,0,0"
                        Text="{Binding HeartRates.Zone3Left}" 
                        Grid.Row="6" 
                        Grid.Column="2"/>
                    <Label 
                        Margin="0,10,0,0"
                        Text="{Binding HeartRates.Zone3Right}" 
                        Grid.Row="6" 
                        Grid.Column="3"/>
                </Grid>
                <!-- Title -->
                <Label
                    Margin="10"
                    Text="System-Focused Workout Paces"
                    FontSize="20"
                    TextColor="#D96CC6"
                    HorizontalOptions="Start" />
                <!-- Header -->
                <HorizontalStackLayout
                    Margin="0,0,38,0"
                    Spacing="22"
                    HorizontalOptions="End">
                    <Label 
                        Text="ARC-1" 
                        Grid.Row="0" 
                        Grid.Column="1"/>
                    <Label 
                        Text="ARC-2" 
                        Grid.Row="0"
                        Grid.Column="2"/>
                    <Label 
                        Text="ARC-3" 
                        Grid.Row="0" 
                        Grid.Column="3"/>
                    <Label 
                        Text="LTCC" 
                        Grid.Row="0" 
                        Grid.Column="4"/>
                </HorizontalStackLayout>
                <!-- Divider -->
                <BoxView
                    Margin="15"
                    Color="Black"
                    HeightRequest="1"/>
                <!-- Table -->
                <CollectionView
                    ItemsSource="{Binding PaceCharts}">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <VerticalStackLayout>
                                <Grid
                                    RowSpacing="2"
                                    ColumnDefinitions="*, *, *, *, *, *"
                                    RowDefinitions="*, *, *">
                                    <Label
                                        VerticalTextAlignment="Center"
                                        Margin="10,12,0,0"
                                        Text="{Binding System}"/>
                                    <Label
                                        Grid.Column="1"
                                        Margin="10,0,0,0"
                                        Text="min"/>
                                    <Label
                                        Grid.Column="1"
                                        Grid.Row="1"
                                        Margin="10,0,0,0"
                                        Text="max"/>
                                    <!--! Min values -->
                                    <Label
                                        Grid.Column="2"
                                        Margin="0,0,0,0"
                                        Text="{Binding Arc1Min}"/>
                                    <Label
                                        Grid.Column="3"
                                        Text="{Binding Arc2Min}"/>
                                    <Label
                                        Grid.Column="4"
                                        Text="{Binding Arc3Min}"/>
                                    <Label
                                        Grid.Column="5"
                                        Text="{Binding LtccMin}"/>
                                    <!--! Max values -->
                                    <Label
                                        Grid.Column="2"
                                        Grid.Row="1"
                                        Margin="0,0,0,0"
                                        Text="{Binding Arc1Max}"/>
                                    <Label
                                        Grid.Column="3"
                                        Grid.Row="1"
                                        Text="{Binding Arc2Max}"/>
                                    <Label
                                        Grid.Column="4"
                                        Grid.Row="1"
                                        Text="{Binding Arc3Max}"/>
                                    <Label
                                        Grid.Column="5"
                                        Grid.Row="1"
                                        Text="{Binding LtccMax}"/>
                                    <BoxView
                                        Grid.ColumnSpan="6"
                                        Margin="15"
                                        Color="Black"
                                        HeightRequest="1"
                                        Grid.Row="2"/>
                                </Grid>
                            </VerticalStackLayout>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                <!-- Second Header -->
                <HorizontalStackLayout
                    IsVisible="{Binding IsAncrItemsVisible}"
                    Margin="0,50,50,20"
                    Spacing="30"
                    HorizontalOptions="End">
                    <Label 
                        Text="ANRC-1" 
                        Grid.Row="0" 
                        Grid.Column="1"/>
                    <Label 
                        Text="ANRC-2" 
                        Grid.Row="0"
                        Grid.Column="2"/>
                </HorizontalStackLayout>
                <!-- Divider -->
                <BoxView
                    IsVisible="{Binding IsAncrItemsVisible}"
                    Margin="10,0,10,0"
                    Color="Black"
                    HeightRequest="1"
                    Grid.ColumnSpan="5"/>
                <!-- Bottom Table -->
                <CollectionView 
                    ItemsSource="{Binding AnrcList}">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <VerticalStackLayout>
                                <Grid
                                    RowSpacing="2"
                                    ColumnDefinitions="*, *"
                                    RowDefinitions="*, *, *">
                                    <Label
                                        VerticalTextAlignment="Center"
                                        Margin="10,12,0,0"
                                        Text="{Binding System}"/>
                                    <Label
                                        Grid.Column="1"
                                        Margin="10,0,0,0"
                                        Text="min"/>
                                    <Label
                                        Grid.Column="1"
                                        Grid.Row="1"
                                        Margin="10,0,0,0"
                                        Text="max"/>
                                    <!--! Min values -->
                                    <Label
                                        Grid.Column="2"
                                        Margin="0,0,0,0"
                                        Text="{Binding Anrc1Min}"/>
                                    <Label
                                        Grid.Column="3"
                                        Text="{Binding Anrc1Max}"/>
                                    <!--! Max values -->
                                    <Label
                                        Grid.Column="2"
                                        Grid.Row="1"
                                        Margin="0,0,0,0"
                                        Text="{Binding Anrc2Min}"/>
                                    <Label
                                        Grid.Column="3"
                                        Grid.Row="1"
                                        Text="{Binding Anrc2Max}"/>
                                    <BoxView
                                        Grid.ColumnSpan="6"
                                        Margin="15"
                                        Color="Black"
                                        HeightRequest="1"
                                        Grid.Row="2"/>
                                </Grid>
                            </VerticalStackLayout>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                <Button
                    Style="{StaticResource SecondaryButton}"
                    VerticalOptions="End"
                    Margin="20,20,20,5"
                    CornerRadius="12"
                    Text="BACK"
                    Command="{Binding BackCommand}"/>
            </components:TopRoundedBorderLayoutView>
        </ScrollView>
    </Grid>
</local:BaseContentPage>