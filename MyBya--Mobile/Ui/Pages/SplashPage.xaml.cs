using MyBya.Ui.ViewModels;
using MyBya.Ui.ViewModels.TestProtocol;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Helpers;
using MyBya.Interfaces;
using Serilog;

namespace MyBya.Ui.Pages;

public partial class SplashPage : ContentPage
{
    public SplashPage()
    {
        InitializeComponent();
        Loaded += OnPageLoaded;
    }

    private async void OnPageLoaded(object? sender, EventArgs e)
    {
      //todo: remove this when create user is ready
       // await InitApp();
        Application.Current.MainPage =  new MainTabbedPage();
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
    }

    private async Task InitApp()
    {
        await CheckAuthentication();
    }

    private async Task CheckAuthentication()
    {
        try
        {
            var isLoggedIn = await ServiceHelper
                .GetService<IAuthenticationService>()
                .IsUserLoggedInAsync();

            if (isLoggedIn)
            {
                if (Application.Current != null)
                    Application.Current.MainPage = new MainTabbedPage();
            }
            else
            {
                if (Application.Current != null)
                    Application.Current.MainPage = new LoginPage(new LoginViewModel());
            }
        }
        catch (Exception ex)
        {
            // Log error and default to login page
            Log.Logger.Error(ex, "Error checking authentication status");

            await DisplayAlert("Error", "An error occurred while checking authentication status. Please enter your credentials.", "OK");

            if (Application.Current != null)
                Application.Current.MainPage = new LoginPage(new LoginViewModel());
        }
    }
}
