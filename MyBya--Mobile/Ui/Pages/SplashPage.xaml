<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MyBya.Ui.Pages.SplashPage"
             NavigationPage.HasNavigationBar="False">
    
    <Grid>
        <!-- Background Gradient -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                <GradientStop Color="#8B5FBF" Offset="0.0" />
                <GradientStop Color="#6B46C1" Offset="0.5" />
                <GradientStop Color="#553C9A" Offset="1.0" />
            </LinearGradientBrush>
        </Grid.Background>
        
        <!-- Logo Container - matches Android specs -->
        <Frame BackgroundColor="White" 
               CornerRadius="16" 
               HasShadow="True"
               WidthRequest="221"
               HeightRequest="215"
               HorizontalOptions="Center"
               VerticalOptions="Center"
               Padding="30">
            
            <!-- MyBya Logo Image -->
            <Image Source="mybya_logo.png"
                   Aspect="AspectFit"
                   HorizontalOptions="Center"
                   VerticalOptions="Center" />
        </Frame>
    </Grid>
</ContentPage>
