<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="MyBya.Ui.Pages.SignUpPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:MyBya.Ui.ViewModels"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:DataType="viewmodels:SignUpViewModel"
             Title="Create Account"
             BackgroundColor="{DynamicResource PageBackgroundColor}">

    <ContentPage.Behaviors>
        <toolkit:StatusBarBehavior StatusBarColor="{DynamicResource Primary}" StatusBarStyle="LightContent" />
    </ContentPage.Behaviors>

    <ScrollView>
        <Grid RowDefinitions="Auto, *, Auto" Padding="32,0">
            <!-- Header -->
            <StackLayout Grid.Row="0" Spacing="16" Margin="0,32,0,0">
                <Label Text="Create Account"
                       FontSize="28"
                       FontAttributes="Bold"
                       HorizontalTextAlignment="Center"
                       TextColor="{DynamicResource Primary}" />
                <Label Text="Join MyBya and start your fitness journey"
                       FontSize="16"
                       HorizontalTextAlignment="Center"
                       TextColor="{DynamicResource Gray600}"
                       Margin="0,0,0,32" />
            </StackLayout>

            <!-- Sign Up Form -->
            <StackLayout Grid.Row="1" Spacing="20" VerticalOptions="Center">
                <!-- Error Message -->
                <Label Text="{Binding ErrorMessage}"
                       IsVisible="{Binding ErrorMessage, Converter={StaticResource IsNotNullOrEmptyConverter}}"
                       TextColor="{DynamicResource DangerColor}"
                       FontSize="14"
                       HorizontalTextAlignment="Center"
                       Margin="0,0,0,10" />

                <!-- Username Entry -->
                <Border BackgroundColor="{DynamicResource Gray50}"
                        Stroke="{DynamicResource Gray200}"
                        StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="12" />
                    </Border.StrokeShape>
                    <Entry x:Name="UsernameEntry"
                           Text="{Binding Username}"
                           Placeholder="Username"
                           FontSize="16"
                           BackgroundColor="Transparent"
                           Margin="16,12"
                           ReturnType="Next"/>
                </Border>

                <!-- Email Entry -->
                <Border BackgroundColor="{DynamicResource Gray50}"
                        Stroke="{DynamicResource Gray200}"
                        StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="12" />
                    </Border.StrokeShape>
                    <Entry x:Name="EmailEntry"
                           Text="{Binding Email}"
                           Placeholder="Email Address"
                           FontSize="16"
                           BackgroundColor="Transparent"
                           Keyboard="Email"
                           Margin="16,12"
                           ReturnType="Next"/>
                </Border>

                <!-- Password Entry -->
                <Border BackgroundColor="{DynamicResource Gray50}"
                        Stroke="{DynamicResource Gray200}"
                        StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="12" />
                    </Border.StrokeShape>
                    <Grid>
                        <Entry x:Name="PasswordEntry"
                               Text="{Binding Password}"
                               Placeholder="Password (min 8 characters)"
                               FontSize="16"
                               BackgroundColor="Transparent"
                               IsPassword="{Binding IsPasswordVisible, Converter={StaticResource InvertedBoolConverter}}"
                               Margin="16,12,48,12"
                               ReturnType="Next" />
                        <Button Text="{Binding IsPasswordVisible, Converter={StaticResource BoolToPasswordVisibilityConverter}}"
                                Command="{Binding TogglePasswordVisibilityCommand}"
                                BackgroundColor="Transparent"
                                TextColor="{DynamicResource Gray500}"
                                FontSize="14"
                                HorizontalOptions="End"
                                VerticalOptions="Center"
                                Margin="0,0,16,0" />
                    </Grid>
                </Border>

                <!-- Confirm Password Entry -->
                <Border BackgroundColor="{DynamicResource Gray50}"
                        Stroke="{DynamicResource Gray200}"
                        StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="12" />
                    </Border.StrokeShape>
                    <Grid>
                        <Entry x:Name="ConfirmPasswordEntry"
                               Text="{Binding ConfirmPassword}"
                               Placeholder="Confirm Password"
                               FontSize="16"
                               BackgroundColor="Transparent"
                               IsPassword="{Binding IsConfirmPasswordVisible, Converter={StaticResource InvertedBoolConverter}}"
                               Margin="16,12,48,12"
                               ReturnType="Done"
                               ReturnCommand="{Binding SignUpCommand}" />
                        <Button Text="{Binding IsConfirmPasswordVisible, Converter={StaticResource BoolToPasswordVisibilityConverter}}"
                                Command="{Binding ToggleConfirmPasswordVisibilityCommand}"
                                BackgroundColor="Transparent"
                                TextColor="{DynamicResource Gray500}"
                                FontSize="14"
                                HorizontalOptions="End"
                                VerticalOptions="Center"
                                Margin="0,0,16,0" />
                    </Grid>
                </Border>

                <!-- Terms and Conditions -->
                <StackLayout Orientation="Horizontal" Margin="0,10,0,0">
                    <CheckBox IsChecked="{Binding AcceptTerms}"
                              Color="{DynamicResource Primary}" />
                    <Label Text="I agree to the Terms and Conditions and Privacy Policy"
                           FontSize="14"
                           TextColor="{DynamicResource Gray600}"
                           VerticalOptions="Center"
                           Margin="8,0,0,0" />
                </StackLayout>

                <!-- Sign Up Button -->
                <Button Text="Create Account"
                        Command="{Binding SignUpCommand}"
                        BackgroundColor="{DynamicResource Primary}"
                        TextColor="White"
                        FontSize="16"
                        FontAttributes="Bold"
                        HeightRequest="50"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}"
                        Margin="0,20,0,0">
                    <Button.Shadow>
                        <Shadow Brush="{DynamicResource Primary}"
                                Opacity="0.3"
                                Radius="8"
                                Offset="0,4" />
                    </Button.Shadow>
                </Button>

                <!-- Loading Indicator -->
                <ActivityIndicator IsVisible="{Binding IsLoading}"
                                   IsRunning="{Binding IsLoading}"
                                   Color="{DynamicResource Primary}"
                                   HeightRequest="30" />

                <!-- Already have account -->
                <StackLayout Orientation="Horizontal"
                             HorizontalOptions="Center"
                             VerticalOptions="Center"
                             Margin="0,32,0,0">
                    <Label Text="Already have an account? "
                           FontSize="14"
                           TextColor="{DynamicResource Gray600}"
                           VerticalOptions="Center" />
                    <Button Text="Sign In"
                            Command="{Binding NavigateToLoginCommand}"
                            BackgroundColor="Transparent"
                            TextColor="{DynamicResource Primary}"
                            FontSize="14"
                            FontAttributes="Bold"
                            Padding="0"
                            Margin="0"
                            HeightRequest="20"
                            VerticalOptions="Center" />
                </StackLayout>
            </StackLayout>

            <!-- Footer -->
            <StackLayout Grid.Row="2" VerticalOptions="End" HorizontalOptions="Center" Margin="0,0,0,32">
                <Label Text="© 2024 MyBya. All rights reserved."
                       FontSize="12"
                       TextColor="{DynamicResource Gray500}"
                       HorizontalTextAlignment="Center" />
            </StackLayout>
        </Grid>
    </ScrollView>
</ContentPage>
