<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="MyBya.Ui.Pages.ForgotPasswordPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:MyBya.Ui.ViewModels"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:DataType="viewmodels:ForgotPasswordViewModel"
             Title="Reset Password"
             BackgroundColor="{DynamicResource PageBackgroundColor}">

    <ContentPage.Behaviors>
        <toolkit:StatusBarBehavior StatusBarColor="{DynamicResource Primary}" StatusBarStyle="LightContent" />
    </ContentPage.Behaviors>

    <ScrollView>
        <Grid RowDefinitions="*, Auto, *" Padding="32,0">
            <!-- Header -->
            <StackLayout Grid.Row="0" VerticalOptions="End" HorizontalOptions="Center" Spacing="16">
                <Image Source="dotnet_bot.png" 
                       HeightRequest="80" 
                       WidthRequest="80"
                       Aspect="AspectFit" />
                <Label Text="Reset Password" 
                       FontSize="28" 
                       FontAttributes="Bold" 
                       HorizontalTextAlignment="Center" 
                       TextColor="{DynamicResource Primary}" />
                <Label Text="Enter your email address and we'll send you instructions to reset your password" 
                       FontSize="16" 
                       HorizontalTextAlignment="Center" 
                       TextColor="{DynamicResource Gray600}" 
                       Margin="0,0,0,32" />
            </StackLayout>

            <!-- Reset Form -->
            <StackLayout Grid.Row="1" Spacing="20" VerticalOptions="Center">
                <!-- Message -->
                <Label Text="{Binding Message}" 
                       IsVisible="{Binding Message, Converter={StaticResource IsNotNullOrEmptyConverter}}"
                       TextColor="{Binding IsSuccess, Converter={StaticResource BoolToSuccessColorConverter}}"
                       FontSize="14"
                       HorizontalTextAlignment="Center"
                       Margin="0,0,0,10" />

                <!-- Email Entry -->
                <Border BackgroundColor="{DynamicResource Gray50}"
                        Stroke="{DynamicResource Gray200}"
                        StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="12" />
                    </Border.StrokeShape>
                    <Entry x:Name="EmailEntry"
                           Text="{Binding Email}"
                           Placeholder="Email Address"
                           FontSize="16"
                           BackgroundColor="Transparent"
                           Keyboard="Email"
                           Margin="16,12"
                           ReturnType="Done"
                           ReturnCommand="{Binding SendResetEmailCommand}" />
                </Border>

                <!-- Send Reset Email Button -->
                <Button Text="Send Reset Instructions"
                        Command="{Binding SendResetEmailCommand}"
                        BackgroundColor="{DynamicResource Primary}"
                        TextColor="White"
                        FontSize="16"
                        FontAttributes="Bold"
                        HeightRequest="50"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}">
                    <Button.Shadow>
                        <Shadow Brush="{DynamicResource Primary}" 
                                Opacity="0.3" 
                                Radius="8" 
                                Offset="0,4" />
                    </Button.Shadow>
                </Button>

                <!-- Loading Indicator -->
                <ActivityIndicator IsVisible="{Binding IsLoading}"
                                   IsRunning="{Binding IsLoading}"
                                   Color="{DynamicResource Primary}"
                                   HeightRequest="30" />

                <!-- Back to Login -->
                <Button Text="Back to Sign In"
                        Command="{Binding NavigateToLoginCommand}"
                        BackgroundColor="Transparent"
                        TextColor="{DynamicResource Primary}"
                        FontSize="14"
                        HorizontalOptions="Center"
                        Margin="0,20,0,0" />
            </StackLayout>

            <!-- Footer -->
            <StackLayout Grid.Row="2" VerticalOptions="End" HorizontalOptions="Center" Margin="0,0,0,32">
                <Label Text="© 2024 MyBya. All rights reserved." 
                       FontSize="12" 
                       TextColor="{DynamicResource Gray500}" 
                       HorizontalTextAlignment="Center" />
            </StackLayout>
        </Grid>
    </ScrollView>
</ContentPage>
