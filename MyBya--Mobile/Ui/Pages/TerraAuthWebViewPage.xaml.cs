using MyBya.Constants;
using MyBya.Ui.Pages.Common;
using MyBya.Ui.ViewModels;
using Serilog;

namespace MyBya.Ui.Pages;

public partial class TerraAuthWebViewPage : BaseContentPage<TerraAuthWebViewModel>
{
    public TerraAuthWebViewPage(TerraAuthWebViewModel webViewModel)
    {
        InitializeComponent();
        BindingContext = webViewModel;
	}

    private void Button_Clicked(object sender, EventArgs e)
    {
        if (terraAuthWebView.CanGoBack)
            terraAuthWebView.GoBack();
    }

    private async void terraAuthWebView_Navigating(object sender, WebNavigatingEventArgs e)
    {
        if (BindingContext is not TerraAuthWebViewModel viewModel)
            return;

        try
        {
            viewModel.IsBusy = true;

            string url = e.Url;

            if (url.Contains(TerraConstants.AUTH_SUCCESS))
            {
                e.Cancel = true;
                await viewModel.AuthenticationCompleted();
                viewModel.IsBusy = false;
            }
            else if (url.Contains(TerraConstants.AUTH_FAILURE))
            {
                e.Can<PERSON> = true;
                await viewModel.ShowMessageError();
                viewModel.IsBusy = false;
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            await viewModel.ShowMessageError();
            viewModel.IsBusy = false;
        }
    }

    private void terraAuthWebView_Navigated(object sender, WebNavigatedEventArgs e)
    {
        ((TerraAuthWebViewModel)BindingContext).IsBusy = false;
    }
}
