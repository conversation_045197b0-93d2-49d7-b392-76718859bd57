using MyBya.Ui.ViewModels;
using MyBya.Ui.ViewModels.TestProtocol;
using MyBya.Ui.Pages.TestProtocol;

namespace MyBya.Ui.Pages;

public partial class MainTabbedPage : TabbedPage
{
    public MainTabbedPage()
    {
        InitializeComponent();
        
        Children.Add(new NavigationPage(new HomePage(new HomeViewModel())) { Title = "Home", IconImageSource = "house.png" });
        Children.Add(new NavigationPage(new TestProtocolInitialCreationPage(new TestProtocolInitialCreationViewModel())) { Title = "Test", IconImageSource = "runner.png" });
        Children.Add(new NavigationPage(new TrainingSchedulePage(new TrainingScheduleViewModel())) { Title = "Calendar", IconImageSource = "calendar.png" });
        Children.Add(new NavigationPage(new WorkoutHistoryPage(new WorkoutHistoryViewModel())) { Title = "History", IconImageSource = "bar_chart.png" });
        Children.Add(new NavigationPage(new AccountPage(new AccountViewModel())) { Title = "Account", IconImageSource = "user.png" });
    }
}
