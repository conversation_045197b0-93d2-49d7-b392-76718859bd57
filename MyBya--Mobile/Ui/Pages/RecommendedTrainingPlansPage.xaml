<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.RecommendedTrainingPlansPage"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    x:TypeArguments="viewModels:RecommendedTrainingPlansViewModel"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    Title="RecommendedTrainingPlansPage">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            HasNavigationBack="True"
            TitleText="Recommended Training Plans">
            <views:BaseView.MainContent>
                <VerticalStackLayout
                    Spacing="25">
                    <Label
                        TextColor="White"
                        FontSize="18"
                        Text="We recommend the following training plans based on your sport and scores."/>
                    <Label
                        TextColor="White"
                        FontSize="18"
                        Text="When you purchase a plan, your current test power scores will be automatically applied."/>
                    <Grid
                        ColumnSpacing="20"
                        ColumnDefinitions="Auto, *">
                        <Grid
                            Rotation="-10">
                            <Image
                                WidthRequest="65"
                                HeightRequest="65"
                                Source="ic_star.png"/>
                                <VerticalStackLayout
                                    HorizontalOptions="Center"
                                    VerticalOptions="Center">
                                      <Label
                                        FontFamily="RobotoBold"
                                        HorizontalTextAlignment="Center"
                                        VerticalTextAlignment="Center"
                                        FontSize="19"
                                        TextColor="White"
                                        FontAttributes="Bold"
                                        Text="20%"/>
                                    <Label
                                        FontFamily="RobotoBold"
                                        HorizontalTextAlignment="Center"
                                        VerticalTextAlignment="Center"
                                        FontSize="13"
                                        TextColor="White"
                                        FontAttributes="Bold"
                                        Text="OFF"/>
                                </VerticalStackLayout>
                        </Grid>
                        <Label
                            HorizontalTextAlignment="Start"
                            Grid.Column="1"
                            TextColor="White"
                            FontSize="17"
                            Text="Your [Subscription level name] comes with a discount on any 12 week training plan (applied at checkout.)"/>
                    </Grid>
                    <CollectionView
                        ItemsSource="{Binding Items}">
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <StackLayout>
                                     <Label
                                        Text="{Binding SportName}"
                                        FontSize="20"
                                        TextColor="White"
                                        FontFamily="RobotoBold">
                                        <Label.Margin>
                                            <OnPlatform x:TypeArguments="Thickness">
                                                <On Platform="iOS" Value="0,20,0,10"/>
                                                <On Platform="Android" Value="0,0,0,0"/>
                                            </OnPlatform>
                                        </Label.Margin>
                                    </Label>
                                    <CollectionView
                                        ItemsSource="{Binding Plans}">
                                        <CollectionView.ItemTemplate>
                                            <DataTemplate>
                                                <StackLayout>
                                                    <Border
                                                        StrokeShape="RoundRectangle 12"
                                                        StrokeThickness="1"
                                                        Margin="0,15,0,0"
                                                        Padding="16"
                                                        BackgroundColor="White">
                                                        <VerticalStackLayout
                                                            Spacing="12">
                                                            <Label
                                                                FontAttributes="Bold"
                                                                FontFamily="RobotoBold"
                                                                Text="{Binding Name}"
                                                                FontSize="18"
                                                                TextColor="Black" />
                                                            <Label
                                                                HorizontalTextAlignment="Start"
                                                                Text="{Binding Description}"
                                                                FontSize="17"
                                                                TextColor="Black" />
                                                            <Button
                                                                Text="DETAILS"
                                                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewModels:RecommendedTrainingPlansViewModel}}, Path=PlanDetailsCommand}"
                                                                CommandParameter="{Binding .}"
                                                                Style="{StaticResource PrimaryButton}"
                                                                HorizontalOptions="Center"
                                                                WidthRequest="150"/>
                                                        </VerticalStackLayout>
                                                    </Border>
                                                </StackLayout>
                                            </DataTemplate>
                                        </CollectionView.ItemTemplate>
                                    </CollectionView>
                                </StackLayout>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </VerticalStackLayout>
            </views:BaseView.MainContent>
        </views:BaseView>
    </Grid>
</local:BaseContentPage>
