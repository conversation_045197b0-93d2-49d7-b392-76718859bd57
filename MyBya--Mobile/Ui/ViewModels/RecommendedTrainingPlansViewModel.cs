using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.Input;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels;

public class RecommendedTrainingPlansViewModel : ListViewModelBase<TrainingPlanGroup>
{
    private List<TrainingPlanTemplateModel> trainingPlans = new List<TrainingPlanTemplateModel>();
    public IAsyncRelayCommand PlanDetailsCommand { get; set; }

    public RecommendedTrainingPlansViewModel()
    {
        PlanDetailsCommand = new AsyncRelayCommand<object>(GoToPreviewTrainingPlan);
    }

    private async Task GoToPreviewTrainingPlan(object? item)
    {
        if (item is TrainingPlanTemplateModel trainingPlanGroup && trainingPlanGroup != null)
        {
            trainingPlanGroup.ShowPriceInfo = true;
            await _navigationService.NavigateToPage<PreviewIndividualPlanPage>(trainingPlanGroup);
        }
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();

        var plansBySport = trainingPlans
            .GroupBy(plan => plan.Sport ?? 0)
            .ToList();

        var groupedPlans = plansBySport
            .Select(g => new TrainingPlanGroup(TrainingPlanHelper.GetSportName(g.Key), g.ToList()))
            .ToList();

        Items = new ObservableCollection<TrainingPlanGroup>(groupedPlans);
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        if (parameter is null)
            throw new ArgumentNullException(nameof(parameter), "Parameter cannot be null.");

        if (parameter is List<TrainingPlanTemplateModel> plans)
            trainingPlans = plans;

        return base.OnNavigatingTo(parameter);
    }
}
