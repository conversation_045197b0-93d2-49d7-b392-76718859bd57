using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Parameters;
using MyBya.Services;
using MyBya.Shared;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels;

public class TrainingScheduleViewModel : ViewModelBase
{
    public IAsyncRelayCommand ContinueCommand { get; }

    public TrainingScheduleViewModel()
    {
        ContinueCommand = new AsyncRelayCommand<object>(ContinueAsync);
    }

    private async Task ContinueAsync(object? value)
    {
        if (value is null)
            return;

        string valueString = value?.ToString() ?? string.Empty;

        if (string.IsNullOrEmpty(valueString))
            return;

        string convertedDate = DateTime.Parse(valueString).ToString("yyyy-MM-dd");

        List<TrainingPlanDetailModel> workouts = await ServiceHelper
            .GetService<TrainingPlanDetailService>()
            .GetTrainingPlansByMemberIdAndDate(memberId: MyByaContext.Instance.GetMemberId(), convertedDate);

        if (workouts is null || workouts.Count == 0)
        {
            await _alertService.ShowAlertAsync("MyBYa", "No workouts found for the selected date.");
            return;
        }

        var trainingPlanDetailDTO = new TrainingPlanDetailParameters()
        {
            SelectedDate = value?.ToString() ?? string.Empty,
            Workouts = workouts
        };

        await _navigationService.NavigateToPage<TrainingScheduleResultPage>(trainingPlanDetailDTO);
    }
}
