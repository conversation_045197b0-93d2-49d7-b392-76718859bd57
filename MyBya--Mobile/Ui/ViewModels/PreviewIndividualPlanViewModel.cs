using CommunityToolkit.Mvvm.Input;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Services;
using MyBya.Ui.ViewModels.Common;
using MyBya.Ui.Views;
using MyBya.Services.TrainingPlanDetail;
using MyBya.Services.DTOs.TrainingPlanDetail;
using MyBya.Shared;
using Serilog;
using MyBya.Services.DTOs;
using MyBya.Ui.Pages;

namespace MyBya.Ui.ViewModels;

public class PreviewIndividualPlanViewModel : ViewModelBase
{
    private TrainingPlanTemplateModel trainingPlanTemplate;

    private string titleText;
    public string TitleText
    {
        get => titleText;
        set => SetProperty(ref titleText, value);
    }

    private string descriptionText;
    public string DescriptionText
    {
        get => descriptionText;
        set => SetProperty(ref descriptionText, value);
    }

    private bool showPriceInfo;
    public bool ShowPriceInfo
    {
        get => showPriceInfo;
        set => SetProperty(ref showPriceInfo, value);
    }

    private string endDateText;
    public string EndDateText
    {
        get => endDateText;
        set => SetProperty(ref endDateText, value);
    }

    public IAsyncRelayCommand BuyPlanCommand { get; set; }

    public PreviewIndividualPlanViewModel()
    {
        titleText = string.Empty;
        descriptionText = string.Empty;
        BuyPlanCommand = new AsyncRelayCommand(BuyPlan);
        trainingPlanTemplate = new TrainingPlanTemplateModel();
    }

    private async Task BuyPlan()
    {
        var popup = ServiceHelper.GetService<SelectDatePopup>();
        popup.callback = GeneratePlanCallback;
        await ShowPopupAsync(popup);

    }

    private async Task GeneratePlanCallback(DateTime date)
    {
        try
        {
            IsBusy = true;

            var request = new GenerateTrainingPlanRequestDto
            {
                MemberId = MyByaContext.Instance.GetMemberId(),
                TemplateId = trainingPlanTemplate.Id,
                StartDate = date
            };

            GenerateTrainingPlanModel? model = await ServiceHelper
                .GetService<GenerateTrainingPlanFromTemplateService>()
                .GenerateTrainingPlanFromTemplate(request);

            if (model == null)
            {
                await ShowMessageError();
                return;
            }

            GoToCalendarTab();
        }
        catch (Exception ex)
        {
            await ShowMessageError(ex.Message);
            Log.Logger.Error(ex, ex.Message);
        }
        finally
        {
            IsBusy = false;
        }
    }

    private void GoToCalendarTab()
    {
        var currentTabbedPage = NavigationHelper.GetCurrentTabbedPage();

        if (currentTabbedPage != null &&
            currentTabbedPage.Children != null)
        {
            currentTabbedPage.CurrentPage = currentTabbedPage.Children[2];
        }
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();

        IsBusy = true;

        TrainingPlanTemplateModel? model = await ServiceHelper
            .GetService<TrainingPlanTemplateService>()
            .GetTrainingPlanTemplatesById(trainingPlanTemplate.Id);

        TitleText = model?.Name ?? string.Empty;
        DescriptionText = model?.Description ?? string.Empty;

        IsBusy = false;
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        if (parameter != null && parameter is TrainingPlanTemplateModel trainingPlan)
        {
            trainingPlanTemplate = trainingPlan;
            ShowPriceInfo = trainingPlan.ShowPriceInfo;

            if (!ShowPriceInfo)
            {
                EndDateText = trainingPlan?.EndDate.HasValue == true
                    ? $"End Date: {trainingPlan.EndDate.Value.ToString("D")}"
                    : "";
            }
        }

        return base.OnNavigatingTo(parameter);
    }
}
