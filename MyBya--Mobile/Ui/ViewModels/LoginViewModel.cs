using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MyBya.Interfaces;
using MyBya.Models;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;
using MyBya.Ui.ViewModels.Common;
using MyBya.Ui.Pages;
using MyBya.Helpers;
using Serilog;

namespace MyBya.Ui.ViewModels;

public class LoginViewModel : ViewModelBase
{
    private readonly IAuthenticationService _authenticationService;

    private string _username = string.Empty;
    public string Username
    {
        get => _username;
        set => SetProperty(ref _username, value);
    }

    private string _password = string.Empty;
    public string Password
    {
        get => _password;
        set => SetProperty(ref _password, value);
    }

    public IAsyncRelayCommand LoginCommand { get; }
    public IAsyncRelayCommand NavigateToSignUpCommand { get; }
    public IAsyncRelayCommand NavigateToForgotPasswordCommand { get; }

    public LoginViewModel()
    {
        _authenticationService = ServiceHelper.GetService<IAuthenticationService>();
        LoginCommand = new AsyncRelayCommand(LoginAsync);
        NavigateToSignUpCommand = new AsyncRelayCommand(NavigateToSignUpAsync);
        NavigateToForgotPasswordCommand = new AsyncRelayCommand(NavigateToForgotPasswordAsync);
    }

    private async Task LoginAsync()
    {
        try
        {
            IsBusy = true;

            if (string.IsNullOrWhiteSpace(Username) ||
                string.IsNullOrWhiteSpace(Password))
            {
                await ShowMessageError("Username and Password cannot be empty.");
                return;
            }

            var result = await _authenticationService
                .LoginAsync(Username.ToLower(), Password);

            if (result == null)
            {
                await ShowMessageError();
                return;
            }

            if (result.Success)
            {
                if (Application.Current != null)
                    Application.Current.MainPage = new MainTabbedPage();
            }
            else
            {
                await ShowMessageError("Login failed. Please check your credentials.");
            }
        }
        catch (Exception ex)
        {
            await ShowMessageError();
            Log.Logger.Error(ex, "Error during login");
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task NavigateToSignUpAsync()
    {
        await _navigationService.NavigateToPage<SignUpPage>();
    }

    private async Task NavigateToForgotPasswordAsync()
    {
        await _navigationService.NavigateToPage<ForgotPasswordPage>();
    }
}
