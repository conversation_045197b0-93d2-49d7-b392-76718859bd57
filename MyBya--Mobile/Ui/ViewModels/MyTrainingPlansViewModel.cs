using System.Collections.ObjectModel;
using System.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Services;
using MyBya.Shared;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;
using Serilog;
namespace MyBya.Ui.ViewModels;

public class MyTrainingPlansViewModel : ListViewModelBase<TrainingPlanGroup>
{
    private bool isEmptyView;
    public bool IsEmptyView
    {
        get => isEmptyView;
        set => SetProperty(ref isEmptyView, value);
    }

    private bool isNotEmptyView;
    public bool IsNotEmptyView
    {
        get => isNotEmptyView;
        set => SetProperty(ref isNotEmptyView, value);
    }

    public IAsyncRelayCommand AddTrainingPlanCommand { get; set; }
    public IAsyncRelayCommand PlanDetailsCommand { get; set; }

    public MyTrainingPlansViewModel()
    {
        AddTrainingPlanCommand = new AsyncRelayCommand(OnAddTrainingPlan);
        PlanDetailsCommand = new AsyncRelayCommand<TrainingPlanTemplateModel>(OnPlanDetails);
    }

    private async Task OnPlanDetails(TrainingPlanTemplateModel? model)
    {
        try
        {
            await _navigationService.NavigateToPage<PreviewIndividualPlanPage>(model);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            await ShowMessageError();
        }
    }

    private async Task OnAddTrainingPlan()
    {
        IsBusy = true;

        AthleteTestSetupModel? athleteTest = await ServiceHelper
            .GetService<AthleteTestSetupService>()
            .GetAthleteTestById(MyByaContext.Instance.CurrentTestSetup?.Id ?? 0);

        if (athleteTest == null)
        {
            await ShowMessageError();
            IsBusy = false;
            return;
        }

        int sport = athleteTest.Sport ?? 0;
        int level = athleteTest.Level ?? 0;
        int eventId = athleteTest.TrainingPlanEventId ?? 0;
        int systems = athleteTest.Systems ?? 0;

        if (systems <= 4)
            systems = 4;
        else if (systems >= 5)
            systems = 6;

        var plans = await ServiceHelper
            .GetService<TrainingPlanTemplateService>()
            .GetTrainingPlanTemplates(sport, level, eventId, systems);

        if (plans is null || plans?.Count == 0)
        {
            await _alertService.ShowAlertAsync("MyBya", "No matching training plans available.");
            IsBusy = false;
            return;
        }

        await _navigationService.NavigateToPage<RecommendedTrainingPlansPage>(plans);
        IsBusy = false;
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();

        IsBusy = true;

        var trainingPlans = await ServiceHelper
            .GetService<UserTrainingPlanService>()
            .GetTrainingPlanByMemberId(MyByaContext.Instance.GetMemberId());

        if (trainingPlans is null || trainingPlans?.Count == 0)
        {
            IsBusy = false;
            IsEmptyView = true;
            Items = [];
            await ShowMessageError("No training plans found.");
            return;
        }

          var plansBySport = trainingPlans?
           .GroupBy(plan => plan?.Sport ?? 0)
           .ToList();

        var groupedPlans = plansBySport?
            .Select(g => new TrainingPlanGroup(TrainingPlanHelper.GetSportName(g.Key), g.ToList()))
            .ToList();

        Items = new ObservableCollection<TrainingPlanGroup>(groupedPlans ?? []);
        IsBusy = false;
    }

    protected override void ViewModelBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        base.ViewModelBase_PropertyChanged(sender, e);

        if (e.PropertyName == nameof(IsEmptyView))
        {
            IsNotEmptyView = !IsEmptyView;
        }
    }
}
