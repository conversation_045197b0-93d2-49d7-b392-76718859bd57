using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MyBya.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;

namespace MyBya.Ui.ViewModels;

public partial class SignUpViewModel : ObservableValidator
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<SignUpViewModel> _logger;

    [ObservableProperty]
    [Required(ErrorMessage = "Username is required")]
    private string username = string.Empty;

    [ObservableProperty]
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    private string email = string.Empty;

    [ObservableProperty]
    [Required(ErrorMessage = "Password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters long")]
    private string password = string.Empty;

    [ObservableProperty]
    [Required(ErrorMessage = "Please confirm your password")]
    private string confirmPassword = string.Empty;

    [ObservableProperty]
    private bool isLoading = false;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    [ObservableProperty]
    private bool isPasswordVisible = false;

    [ObservableProperty]
    private bool isConfirmPasswordVisible = false;

    [ObservableProperty]
    private bool acceptTerms = false;

    public SignUpViewModel(IAuthenticationService authenticationService, ILogger<SignUpViewModel> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    [RelayCommand]
    private async Task SignUpAsync()
    {
        try
        {
            // IsLoading = true;
            // ErrorMessage = string.Empty;

            // if (!ValidateForm())
            // {
            //     return;
            // }

            // var result = await _authenticationService.RegisterAsync(Username, Email, Password);

            // if (result.IsSuccess)
            // {
            //     await Shell.Current.GoToAsync("//MainTabbedPage");
            // }
            // else
            // {
            //     ErrorMessage = result.ErrorMessage ?? "Registration failed";
            // }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during registration");
            ErrorMessage = "An error occurred during registration";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task NavigateToLoginAsync()
    {
        await Shell.Current.GoToAsync("..");
    }

    [RelayCommand]
    private void TogglePasswordVisibility()
    {
        IsPasswordVisible = !IsPasswordVisible;
    }

    [RelayCommand]
    private void ToggleConfirmPasswordVisibility()
    {
        IsConfirmPasswordVisible = !IsConfirmPasswordVisible;
    }

    private bool ValidateForm()
    {
        if (string.IsNullOrWhiteSpace(Username))
        {
            ErrorMessage = "Username is required";
            return false;
        }

        if (string.IsNullOrWhiteSpace(Email))
        {
            ErrorMessage = "Email is required";
            return false;
        }

        if (!IsValidEmail(Email))
        {
            ErrorMessage = "Please enter a valid email address";
            return false;
        }

        if (string.IsNullOrWhiteSpace(Password))
        {
            ErrorMessage = "Password is required";
            return false;
        }

        if (Password.Length < 8)
        {
            ErrorMessage = "Password must be at least 8 characters long";
            return false;
        }

        if (Password != ConfirmPassword)
        {
            ErrorMessage = "Passwords do not match";
            return false;
        }

        if (!AcceptTerms)
        {
            ErrorMessage = "Please accept the terms and conditions";
            return false;
        }

        return true;
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
