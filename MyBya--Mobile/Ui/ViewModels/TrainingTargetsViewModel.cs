using System.Collections.ObjectModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using MyBya.DataAccess.Interfaces;
using MyBya.Entities;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Repository.Interface;
using MyBya.Services;
using MyBya.Shared;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.ViewModels;

public class TrainingTargetsViewModel : ViewModelBase
{
    private HeartRatesModel? heartRates;
    public HeartRatesModel? HeartRates
    {
        get => heartRates;
        set => SetProperty(ref heartRates, value);
    }

    private ObservableCollection<TestResultPaceChartModel> paceCharts;
    public ObservableCollection<TestResultPaceChartModel> PaceCharts
    {
        get => paceCharts;
        set => SetProperty(ref paceCharts, value);
    }

    public IAsyncRelayCommand ContinueCommand { get; set; }

    public TrainingTargetsViewModel()
    {
        paceCharts = new ObservableCollection<TestResultPaceChartModel>();
        ContinueCommand = new AsyncRelayCommand(Continue);
    }

    private async Task Continue()
    {
        try
        {
            IsBusy = true;
            await _navigationService.NavigateToPage<TrainingSchedulePage>();
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
        }
        finally
        {
            IsBusy = false;
        }
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();

        try
        {
            IsBusy = true;

            await SetHeartRates();
            await SetPaceCharts();
            await SetAnrcs();

            IsBusy = false;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
        }
    }

    private async Task SetAnrcs()
    {
        var anrcsItems = await ServiceHelper
            .GetService<TestResultAnrcService>()
            .GetTestResultsAnrc();

        // it doesnt work for PPT protocol. Waiting for the new ones to update the table.
    }

    private async Task SetPaceCharts()
    {
        int testId = MyByaContext.Instance.CurrentTestSetup?.Id ?? 0;

        var entites = await ServiceHelper
            .GetService<ITestResultPaceChartRepository>()
            .SelectByCriteria(x => x.AthleteTestSetupId == testId);

        var paceChartList = new List<TestResultPaceChartModel>();

        foreach (var entity in entites)
        {
            var model = Mapper.Map<TestResultPaceChartModel>(entity);
            paceChartList.Add(model);
        }

        PaceCharts = new ObservableCollection<TestResultPaceChartModel>(paceChartList);
    }

    public async Task SetHeartRates()
    {
        var testId = MyByaContext.Instance.CurrentTestSetup?.Id ?? 0;

        var heartRateEntity = await ServiceHelper
            .GetService<ITestResultHeartRateRepository>()
            .SelectByCriteria(x => x.AthleteTestSetupId == testId);

        if (heartRateEntity is null || !heartRateEntity.Any())
        {
            await ShowMessageError();
            return;
        }

        var heartRatesResult = Mapper.Map<TestResultHeartRateModel>(heartRateEntity.FirstOrDefault());
        HeartRatesModel heartRates = GetHeartRates(heartRatesResult);
        HeartRates = heartRates;
    }

    private HeartRatesModel GetHeartRates(TestResultHeartRateModel
         testResultHeartRateModel)
    {
        int lowFirst = testResultHeartRateModel.Zone2Min ?? 0;
        int lowLast = lowFirst + 6;
        int midFirst = lowLast + 1;
        int midLast = midFirst + 6;
        int highFirst = midLast + 1;
        int highLast = highFirst + 6;

        var heartRates = new HeartRatesModel
        {
            Zone1 = testResultHeartRateModel.Zone1Max ?? 0,
            Zone2LowLeft = lowFirst,
            Zone2LowRight = lowLast,
            Zone2MidLeft = midFirst,
            Zone2MidRight = midLast,
            Zone2HighLeft = highFirst,
            Zone2HighRight = highLast,
            Zone3Left = testResultHeartRateModel.Zone3Min ?? 0,
            Zone3Right = testResultHeartRateModel.Zone3Max ?? 0
        };

        return heartRates;
    }

    private async Task SetHeartRatesLocally(TestResultHeartRateModel? heartRates)
    {
        if (heartRates is null)
        {
            throw new ArgumentNullException(nameof(heartRates), "Heart rates cannot be null.");
        }

        int lowFirst = heartRates?.Zone2Min ?? 0;
        int lowLast = lowFirst + 6;
        int midFirst = lowLast + 1;
        int midLast = midFirst + 6;
        int highFirst = midLast + 1;
        int highLast = highFirst + 6;

        heartRates.Zone1 = heartRates.Zone1Max.HasValue
            ? $"{heartRates.Zone1Max.Value}"
            : "";

        heartRates.Zone2Low = $"{lowFirst} - {lowLast}";
        heartRates.Zone2Mid = $"{midFirst} - {midLast}";
        heartRates.Zone2High = $"{highFirst} - {highLast}";
        heartRates.Zone3 = $"{heartRates.Zone3Min ?? 0} - {heartRates.Zone3Max ?? 0}";

        var entity = Mapper.Map<TestResultHeartRatesEntity>(heartRates);

        var testResultHeartRateDataAccess = ServiceHelper
            .GetService<ITestResultHeartRateRepository>();

        await testResultHeartRateDataAccess.Clear();
        await testResultHeartRateDataAccess.Insert(entity);
    }

    private async Task SetPaceChartsLocally(List<TestResultPaceChartModel> paceCharts)
    {
        var testResultPaceChartDataAccess = ServiceHelper.GetService<ITestResultPaceChartRepository>();
        await testResultPaceChartDataAccess.Clear();

        var entites = new List<TestResultPaceChartEntity>();

        foreach (var model in paceCharts)
        {
            var entity = Mapper.Map<TestResultPaceChartEntity>(model);
            entites.Add(entity);
        }

        await testResultPaceChartDataAccess.InsertAll(entites);
    }
}
