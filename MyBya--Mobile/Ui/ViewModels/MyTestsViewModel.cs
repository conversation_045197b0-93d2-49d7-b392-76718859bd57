using System.Collections.ObjectModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using MyBya.Enums;
using MyBya.Services;
using MyBya.Services.DTOs;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.ViewModels;

public class MyTestsViewModel : ViewModelBase
{
    private readonly CurrentTestWithCalendarService _currentTestWithCalendarService;
    public ObservableCollection<TestItem> Tests { get; set; }

    public MyTestsViewModel(CurrentTestWithCalendarService currentTestWithCalendarService)
    {
        _currentTestWithCalendarService = currentTestWithCalendarService;
        Tests = new ObservableCollection<TestItem>();
        LoadTestStatusFromApi();
    }

    private async void LoadTestStatusFromApi()
    {
        try
        {
            IsBusy = true;
            Log.Logger.Information("Loading current test status info from API");

            // Define sports in the required order: Running, Cycling, Swimming, Rowing
            var orderedSports = new[]
            {
                SportEnum.RUNNING,
                SportEnum.CYCLING,
                SportEnum.SWIMMING,
                SportEnum.ROWING
            };

            var apiTests = await _currentTestWithCalendarService.GetCurrentTestsWithCalendar();

            if (apiTests == null)
            {
                throw new Exception("API returned null response");
            }

            Tests.Clear();
            foreach (var sport in orderedSports)
            {
                // Find matching API test for this sport
                var apiTest = apiTests.FirstOrDefault(t => t.Sport == sport);

                var testItem = new TestItem
                {
                    Name = sport.ToString(),
                    TestDate = apiTest?.CalendarDate.ToString("M/d/yyyy") ?? "",
                    CanRetakeDate = apiTest?.CanRetakeDate ?? "",
                    CanTakeTest = apiTest?.CanRetake ?? true
                };
                Tests.Add(testItem);
            }

            Log.Logger.Information("Successfully loaded the status info for {Count} tests API", Tests.Count);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Failed to load test status items from API: {Message}", ex.Message);
        }
        finally
        {
            IsBusy = false;
        }
    }
    public ICommand TakeTestCommand => new AsyncRelayCommand<TestItem>(OnTakeTest);

    private async Task OnTakeTest(TestItem? test)
    {
        if (test != null)
        {
            // Convert test name to SportEnum
            if (Enum.TryParse<SportEnum>(test.Name, out var sport))
            {
                await _navigationService.NavigateToPage<TestProtocolInitialCreationPage>(sport);
            }
        }
    }
}

public class TestItem
{
    public string Name { get; set; } = string.Empty;
    public string TestDate { get; set; } = string.Empty;
    public string CanRetakeDate { get; set; } = string.Empty;
    public bool CanTakeTest { get; set; }
}
