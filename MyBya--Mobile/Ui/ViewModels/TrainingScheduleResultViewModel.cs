using System.Collections.ObjectModel;
using System.Text;
using System.Text.Json;
using MyBya.Constants;
using MyBya.DataAccess.Interfaces;
using MyBya.Entities;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Parameters;
using MyBya.Repository.Interface;
using MyBya.Shared;
using MyBya.Ui.ViewModels.Common;
using Newtonsoft.Json.Linq;
using Serilog;
namespace MyBya.Ui.ViewModels;
public partial class TrainingScheduleResultViewModel : ViewModelBase
{
    private List<TrainingPlanDetailModel> _workouts;
    private ObservableCollection<WorkoutsModel> workouts;
    public ObservableCollection<WorkoutsModel> Workouts
    {
        get => workouts;
        set => SetProperty(ref workouts, value);
    }
    private string titleText;
    public string TitleText
    {
        get => titleText;
        set => SetProperty(ref titleText, value);
    }

    private Dictionary<string, string> fixedDictionary { get; set; }

    public TrainingScheduleResultViewModel()
    {
        _workouts = new List<TrainingPlanDetailModel>();
        workouts = new ObservableCollection<WorkoutsModel>();
        titleText = string.Empty;
        Workouts = new ObservableCollection<WorkoutsModel>();
        fixedDictionary = new Dictionary<string, string>();
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        var trainingPlanDetail = parameter as TrainingPlanDetailParameters;

        if (trainingPlanDetail == null)
            return Task.CompletedTask;

        TitleText = trainingPlanDetail.SelectedDate ?? string.Empty;
        _workouts = trainingPlanDetail.Workouts ?? new List<TrainingPlanDetailModel>();

        return base.OnNavigatingTo(parameter);
    }

    private async Task ProcessWorkout(string workoutName, string comment)
    {
        var valuePairs = BuildWorkoutDictionary(workoutName);
        var currentWorkout = _workouts.FirstOrDefault(x => x.WorkoutName == workoutName);
        var finalString = new StringBuilder();

        var configArray = JArray.Parse(currentWorkout?.Configurable ?? "");
        var fieldsArray = (JArray)((JArray)configArray[0])[1];

        finalString.AppendLine(workoutName);
        finalString.AppendLine(currentWorkout?.WorkoutDescription ?? "");

        ProcessFixed(currentWorkout);
        ProcessWorkoutFields(fieldsArray, valuePairs, finalString);

        foreach (var fixedItem in fixedDictionary)
        {
            switch (fixedItem.Key)
            {
                case "Target Heartrate":
                    await ProcessHeartRate(fixedDictionary["Target Heartrate"], finalString);
                    break;
                case "Pace":
                    await ProcessPace(fixedDictionary, valuePairs, finalString);
                    break;
                case "Session Volume":
                    ProcessSessionVolume(fixedDictionary["Session Volume"], finalString);
                    break;
                default:
                    finalString.AppendLine($"{fixedItem.Key}: {fixedItem.Value}");
                    break;
            }
        }

        Workouts.Add(new WorkoutsModel
        {
            WorkoutComment = comment,
            WorkoutLabel = finalString.ToString()
        });
    }

    private Dictionary<string, string> BuildWorkoutDictionary(string workoutName)
    {
        var workoutList = _workouts
            .Where(w => w.WorkoutName == workoutName && w.FieldName != null && w.FieldValue != null)
            .ToList();

        var valuePairs = new Dictionary<string, string>();

        foreach (var workout in workoutList)
        {
            if (!string.IsNullOrEmpty(workout.FieldName) &&
                !string.IsNullOrEmpty(workout.FieldValue))
            {
                if (!valuePairs.ContainsKey(workout.FieldName))
                    valuePairs.Add(workout.FieldName!, workout.FieldValue);
            }
        }

        return valuePairs;
    }

    private void ProcessFixed(TrainingPlanDetailModel? currentWorkout)
    {
        fixedDictionary.Clear();
        string fixedJson = currentWorkout?.Fixed ?? "";
        var list = JsonSerializer.Deserialize<List<List<string>>>(fixedJson);

        if (list == null || list.Count == 0)
            return;

        foreach (var pair in list)
        {
            if (pair.Count == 2)
            {
                string label = pair[0];
                string value = pair[1];
                fixedDictionary.Add(label, value);
            }
        }
    }

    private void ProcessWorkoutFields(JArray fieldsArray, Dictionary<string, string> dictionary, StringBuilder finalString)
    {
        foreach (var entry in fieldsArray)
        {
            var fieldName = entry[0]?.ToString();
            var cfg = entry[1];

            if (fieldName != null && !IsValidField(fieldName, dictionary))
                continue;

            if (fieldName == "target_heartrate")
            {
                fixedDictionary["Target Heartrate"] = dictionary[fieldName];
            }
            else if (fieldName == "pace")
            {
                fixedDictionary["Pace"] = dictionary[fieldName];
            }
            else if (fieldName == "session_volume")
            {
                fixedDictionary["Session Volume"] = dictionary[fieldName];
            }
            else if (cfg != null && cfg.Type == JTokenType.Object && cfg["label"] != null)
                if (fieldName != null && dictionary.ContainsKey(fieldName))
                {
                    ProcessGenericField(cfg, dictionary[fieldName], finalString);
                }
        }
    }
    private bool IsValidField(string fieldName, Dictionary<string, string> dictionary)
    {
        return !string.IsNullOrWhiteSpace(fieldName)
            && dictionary.ContainsKey(fieldName)
            && !string.IsNullOrEmpty(dictionary[fieldName]);
    }

    private async Task ProcessHeartRate(string heartRateZones, StringBuilder finalString)
    {
        if (heartRateZones == "N/A")
            return;

        var currentTestSetup = MyByaContext.Instance.CurrentTestSetup;

        if (currentTestSetup?.Sport == null)
        {
            Log.Logger.Warning("Unable to process heart rate: Current test setup or sport is not available");
            return;
        }

        var heartRates = await ServiceHelper
            .GetService<ITestResultHeartRateRepository>()
            .SelectByCriteria(x => x.Sport == currentTestSetup.Sport);

        if (heartRates == null || !heartRates.Any())
        {
            Log.Logger.Warning("No heart rate data found for sport {Sport}", currentTestSetup.Sport);
            return;
        }

        var heartRateItem = heartRates.First();

        string result = "";

        if (heartRateZones.Contains("-"))
        {
            foreach (var zone in heartRateZones.Split('-'))
                result += GetHeartRateZoneText(zone.Trim(), heartRateItem);
        }
        else
        {
            result = GetHeartRateZoneText(heartRateZones.Trim(), heartRateItem);
        }

        finalString.AppendLine($"Target Heartrate: {heartRateZones}: {result}");
    }

    private string GetHeartRateZoneText(string zone, TestResultHeartRatesEntity heartRateItem) => zone switch
    {
        HeartRateZonesConstants.ZONE_1 => $"< {heartRateItem.Zone1} - ",
        HeartRateZonesConstants.ZONE_2_LOW => $"[{heartRateItem.Zone2Low}]",
        HeartRateZonesConstants.ZONE_2_MID => $"[{heartRateItem.Zone2Mid}]",
        HeartRateZonesConstants.ZONE_2_HIGH => $"[{heartRateItem.Zone2High}]",
        HeartRateZonesConstants.ZONE_3 => $"[{heartRateItem.Zone3}]",
        _ => string.Empty
    };

    private async Task ProcessPace(Dictionary<string, string> fixedDictionary,
        Dictionary<string, string> valuePairs,
        StringBuilder finalString)
    {
        if (!valuePairs.TryGetValue("interval_distance", out var intervalDistance))
            return;

        if (!fixedDictionary.TryGetValue("Pace", out var paceFieldName))
            return;

        var paces = await ServiceHelper
            .GetService<ITestResultPaceChartRepository>()
            .GetPacesByInterval(intervalDistance);

        string paceText = GetPaceText(paceFieldName, intervalDistance, paces);

        if (!string.IsNullOrEmpty(paceText))
        {
            finalString.AppendLine($"Interval Distance: {intervalDistance}");
            finalString.AppendLine($"Pace: {paceText}");
        }
    }

    private string GetPaceText(string paceType, string distance, TestResultPaceChartModel pace)
    {
        return paceType switch
        {
            PacesConstants.ARC1 => $"{paceType} ({pace.Arc1Min} - {pace.Arc1Max})",
            PacesConstants.ARC2 => $"{paceType} ({pace.Arc2Min} - {pace.Arc2Max})",
            PacesConstants.ARC3 => $"{paceType} ({pace.Arc3Min} - {pace.Arc3Max})",
            PacesConstants.LTCC => $"{paceType} ({pace.LtccMin} - {pace.LtccMax})",
            _ => string.Empty
        };
    }

    private void ProcessSessionVolume(string sessionVolumeValue, StringBuilder finalString)
    {
        finalString.AppendLine($"Session Volume: {sessionVolumeValue}");
    }

    private void ProcessGenericField(JToken cfg, string fieldValue, StringBuilder finalString)
    {
        string label = cfg.Value<string>("label") ?? string.Empty;
        fixedDictionary[label] = fieldValue;
    }

    public override async Task OnAppearing()
    {
        try
        {
            await base.OnAppearing();

            List<string> workoutNames = _workouts != null
                ? _workouts
                    .Select(x => x.WorkoutName)
                    .Where(name => name != null)
                    .Distinct()
                    .Cast<string>()
                    .ToList()
                : new List<string>();

            if (workoutNames is null)
                return;

            if (workoutNames.FirstOrDefault() == "Day Off")
            {
                Workouts.Add(new WorkoutsModel { WorkoutLabel = "Day Off" });
                return;
            }

            string comment = "";

            if (_workouts != null && !string.IsNullOrEmpty(_workouts.FirstOrDefault()?.Comment))
                comment = _workouts.FirstOrDefault()?.Comment ?? string.Empty;

            foreach (string workoutName in workoutNames)
            {
                await ProcessWorkout(workoutName, comment);
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
        }
    }
}
