using System;
using System.Windows.Input;
using MyBya.Constants;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Services.DTOs;
using MyBya.Services.Terra;
using MyBya.Shared;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.ViewModels;

public class TerraAuthWebViewModel : ViewModelBase
{
    private string? terraAuthUrl;
    public string TerraAuthUrl
    {
        get => terraAuthUrl ?? "";
        set => SetProperty(ref terraAuthUrl, value);
    }

    public async override Task OnAppearing()
    {
        try
        {
            await base.OnAppearing();

            IsBusy = true;

            var terraAuthRequestDto = new TerraAuthRequestDto
            {
                Language = "en",
                ReferenceId = MyByaContext.Instance.GetMemberId().ToString(),
                AuthSuccessRedirectUrl = TerraConstants.AUTH_SUCCESS,
                AuthFailureRedirectUrl = TerraConstants.AUTH_FAILURE
            };

            TerraAuthModel terraAuth = await ServiceHelper
                .GetService<TerraAuthService>()
                .GetTerraAuthModelAsync(terraAuthRequestDto);

            if (terraAuth == null || terraAuth.Status == TerraConstants.AUTH_FAILURE)
            {
                await ShowMessageError();
                return;
            }

            if (terraAuth.Url == null)
                return;

            TerraAuthUrl = terraAuth.Url;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            await ShowMessageError("Error during Terra authorization. Please try again.");
        }
        finally
        {
            IsBusy = false;
        }
    }

    public async Task AuthenticationCompleted()
    {
        await ShowToastMessage("Authentication completed successfully!");
        await _navigationService.NavigateBack();
    }
}
