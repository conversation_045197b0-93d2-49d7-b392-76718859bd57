using System.Windows.Input;
using MyBya.Ui.ViewModels.Common;
using MyBya.Ui.Pages;
using MyBya.Shared;
using Serilog;
using MyBya.Helpers;
using MyBya.Services;
using MyBya.DataAccess.Interfaces;
using MyBya.Repository;
using MyBya.Repository.Interface;
using MyBya.Entities;
using MyBya.Models;

namespace MyBya.Ui.ViewModels;

public class HomeViewModel : ViewModelBase
{
    private string _welcomeMessage = "Welcome to MyBya";
    public string WelcomeMessage
    {
        get => _welcomeMessage;
        set => SetProperty(ref _welcomeMessage, value);
    }

    public ICommand NavigateToTestProtocolCommand { get; }
    public ICommand NavigateToCalendarCommand { get; }
    public ICommand NavigateToHistoryCommand { get; }
    public ICommand NavigateToAccountCommand { get; }

    public HomeViewModel()
    {
        NavigateToTestProtocolCommand = new Command(OnNavigateToTestProtocol);
        NavigateToCalendarCommand = new Command(OnNavigateToCalendar);
        NavigateToHistoryCommand = new Command(OnNavigateToHistory);
        NavigateToAccountCommand = new Command(OnNavigateToAccount);
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();
        SyncAthleteData();
    }

    private void SyncAthleteData()
    {
        var syncTask = Task.Run(async () =>
        {
            try
            {
                var items = await GetMostRecentTests();

                if (items?.Count > 0)
                    await ProcessAthleteTests(items);
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, ex.Message);
                throw;
            }
        })
        .ContinueWith(task =>
        {
            if (task.Exception != null)
                Log.Logger.Error(task.Exception, "Error occurred during athlete data synchronization.");
        });
    }

    private async Task<List<AthleteTestSetupModel>> GetMostRecentTests()
    {
        return await ServiceHelper
            .GetService<AthleteTestSetupService>()
            .GetMostRecentTestsByMemberId(MyByaContext.Instance.GetMemberId());
    }

    private async Task ProcessAthleteTests(List<AthleteTestSetupModel> items)
    {
        foreach (var item in items)
        {
            var test = CreateTestEntity(item);
            var localTestSetup = await GetLocalTestSetup(item);

            await SyncTestSetup(item, localTestSetup);

            bool isOutdated = IsTestDataOutdated(localTestSetup, item);
            await SyncTestData(test, isOutdated);
        }
    }

    private AthleteTestSetupEntity CreateTestEntity(AthleteTestSetupModel item)
    {
        return new AthleteTestSetupEntity
        {
            Id = item.Id,
            Sport = item.Sport,
            TestDetailAthleteId = item.TestDetailAthleteId
        };
    }

    private async Task<IList<AthleteTestSetupEntity>> GetLocalTestSetup(AthleteTestSetupModel item)
    {
        return await ServiceHelper
            .GetService<IAthleteTestSetupRepository>()
            .SelectByCriteria(x => x.Id == item.Id && x.Sport == item.Sport);
    }

    private async Task SyncTestSetup(AthleteTestSetupModel item, IList<AthleteTestSetupEntity> localTestSetup)
    {
        if (localTestSetup?.Count == 0)
        {
            var newTestSetup = Mapper.Map<AthleteTestSetupEntity>(item);

            await ServiceHelper
                .GetService<IAthleteTestSetupRepository>()
                .Insert(newTestSetup);
        }
    }

    private bool IsTestDataOutdated(IList<AthleteTestSetupEntity> localTestSetup, AthleteTestSetupModel item)
    {
        return localTestSetup != null &&
            (localTestSetup.FirstOrDefault()?.CreationTime < item.CreationTime ||
             localTestSetup.FirstOrDefault()?.UpdatedAt < item.UpdatedAt ||
             localTestSetup.FirstOrDefault()?.LastModificationTime < item.LastModificationTime);
    }

    private async Task SyncTestData(AthleteTestSetupEntity test, bool isOutdated)
    {
        await SyncPaceCharts(test, isOutdated);
        await SyncHeartRates(test, isOutdated);
    }

    private async Task SyncPaceCharts(AthleteTestSetupEntity test, bool isOutdated)
    {
        var paces = await ServiceHelper
            .GetService<ITestResultPaceChartRepository>()
            .SelectByCriteria(x => x.AthleteTestSetupId == test.Id && x.Sport == test.Sport);

        if (paces?.Count == 0 || isOutdated)
        {
            paces?.Clear();
            await PaceChartProcessingShared.BuildPaceCharts(test);
        }
    }

    private async Task SyncHeartRates(AthleteTestSetupEntity test, bool isOutdated)
    {
        var heartRates = await ServiceHelper
            .GetService<ITestResultHeartRateRepository>()
            .SelectByCriteria(x => x.AthleteTestSetupId == test.Id && x.Sport == test.Sport);

        if (heartRates?.Count == 0 || isOutdated)
        {
            heartRates?.Clear();
            await HeartRateProcessingShared.SetHeartRates(test);
        }
    }

    private async void OnNavigateToTestProtocol()
    {
        if (Application.Current.MainPage is MainTabbedPage tabbedPage)
        {
            tabbedPage.CurrentPage = tabbedPage.Children[1];
        }
    }

    private async void OnNavigateToCalendar()
    {
        if (Application.Current.MainPage is MainTabbedPage tabbedPage)
        {
            tabbedPage.CurrentPage = tabbedPage.Children[2];
        }
    }

    private async void OnNavigateToHistory()
    {
         if (Application.Current.MainPage is MainTabbedPage tabbedPage)
        {
            tabbedPage.CurrentPage = tabbedPage.Children[3];
        }
    }

    private async void OnNavigateToAccount()
    {
        if (Application.Current.MainPage is MainTabbedPage tabbedPage)
        {
            tabbedPage.CurrentPage = tabbedPage.Children[4];
        }
    }
}
