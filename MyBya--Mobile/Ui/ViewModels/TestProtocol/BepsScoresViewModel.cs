using System.Collections.ObjectModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using MyBya.Entities;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Repository.Interface;
using MyBya.Services;
using MyBya.Shared;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.ViewModels.TestProtocol;

public class BepsScoresViewModel : ListViewModelBase<BeepScoresModel>
{
    private BepsTestDataModel bepsTestDataModel;
    public BepsTestDataModel CurrentBepsTestData
    {
        get => bepsTestDataModel;
        set => SetProperty(ref bepsTestDataModel, value);
    }

    private string date;
    public string Date
    {
        get => date;
        set => SetProperty(ref date, value);
    }

    public IAsyncRelayCommand ContinueCommand { get; set; }

    public IAsyncRelayCommand ViewTrainingPlansCommand { get; set; }

    public BepsScoresViewModel()
    {
        bepsTestDataModel = new BepsTestDataModel();
        date = string.Empty;
        ContinueCommand = new AsyncRelayCommand(Continue);
        ViewTrainingPlansCommand = new AsyncRelayCommand(ViewRecommendedTrainingPlans);
    }

    private async Task ViewRecommendedTrainingPlans()
    {
        IsBusy = true;

        AthleteTestSetupModel? athleteTest = await ServiceHelper
            .GetService<AthleteTestSetupService>()
            .GetAthleteTestById(MyByaContext.Instance.CurrentTestSetup?.Id ?? 0);

        if (athleteTest == null)
        {
            await ShowMessageError();
            return;
        }

        int sport = athleteTest.Sport ?? 0;
        int level = athleteTest.Level ?? 0;
        int eventId = athleteTest.TrainingPlanEventId ?? 0;
        int systems = athleteTest.Systems ?? 0;

        if (systems <= 4)
            systems = 4;
        else if (systems >= 5)
            systems = 6;

        var plans = await ServiceHelper
            .GetService<TrainingPlanTemplateService>()
            .GetTrainingPlanTemplates(sport, level, eventId, systems);

        if (plans is null)
        {
            await ShowMessageError();
            return;
        }

        if (plans.Count == 0)
        {
            await _alertService.ShowAlertAsync("MyBya", "No matching training plans available.");
            return;
        }

        await _navigationService.NavigateToPage<RecommendedTrainingPlansPage>(plans);
        IsBusy = false;
    }

    private async Task Continue()
    {
        IsBusy = true;

        try
        {
            await _navigationService.NavigateToPage<TrainingTargetsPage>();
        }
        finally
        {
            IsBusy = false;
        }
    }

    public override async Task OnAppearing()
    {
        try
        {
            await base.OnAppearing();

            IsBusy = true;

            if (CurrentBepsTestData is null)
            {
                await ShowMessageError();
                return;
            }

            Items = new ObservableCollection<BeepScoresModel>()
            {
                new (BepsScoresEnum.AF, CurrentBepsTestData.Af),
                new (BepsScoresEnum.PAC, CurrentBepsTestData.Pac),
                new (BepsScoresEnum.LTCCC, CurrentBepsTestData.Ltcc),
                new (BepsScoresEnum.ARC3, CurrentBepsTestData.Arc3),
                new (BepsScoresEnum.ARC2, CurrentBepsTestData.Arc2),
                new (BepsScoresEnum.ARC1, CurrentBepsTestData.Arc1),
                new (BepsScoresEnum.ANRC2, CurrentBepsTestData.Anrc2),
                new (BepsScoresEnum.ANRC1, CurrentBepsTestData.Anrc1)
            };

            Date = MyByaContext.Instance.CurrentTestCalendar != null
                ? MyByaContext.Instance.CurrentTestCalendar.Date?.ToString("MMM dd, yyyy hh:mm tt") ?? string.Empty
                : string.Empty;

            var testSetup = MyByaContext.Instance.CurrentTestSetup;
            var entity = Mapper.Map<AthleteTestSetupEntity>(testSetup);

            await ServiceHelper
                .GetService<IAthleteTestSetupRepository>()
                .SetMostRecentTestSetup(entity);

            await HeartRateProcessingShared.SetHeartRates(entity);
            await PaceChartProcessingShared.BuildPaceCharts(entity);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
        }
        finally
        {
            IsBusy = false;
        }
    }
    public override async Task OnNavigatingTo(object? parameter)
    {
        await base.OnNavigatingTo(parameter);

        if (parameter is null)
            return;

        CurrentBepsTestData = (BepsTestDataModel)parameter;
    }
}
