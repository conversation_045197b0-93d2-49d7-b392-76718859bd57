using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MyBya.Constants;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Services;
using MyBya.Services.DTOs;
using MyBya.Shared;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.ViewModels.TestProtocol;

public class EnterTestDataViewModel : ListViewModelBase<TestCalendarDetailsModel>
{
    private AthleteTestSetupModel? CurrentTestSetup {get; set;}

    private List<TestCalendarDetailsModel> _intervals {get; set;}

    private string standardInterval;
    public string StandardInterval
    {
        get => standardInterval;
        set => SetProperty(ref standardInterval, value);
    }

    private string intervalTypeTitle;
    public string IntervalTypeTitle
    {
        get => intervalTypeTitle;
        set => SetProperty(ref intervalTypeTitle, value);
    }

    private bool isSubmitButtonEnabled;
    public bool IsSubmitButtonEnabled
    {
        get => isSubmitButtonEnabled;
        set => SetProperty(ref isSubmitButtonEnabled, value);
    }

    private bool isAddNextIntervalVisible = true;
    public bool IsAddNextIntervalVisible
    {
        get => isAddNextIntervalVisible;
        set => SetProperty(ref isAddNextIntervalVisible, value);
    }

    private int intervalsCount;

    public IAsyncRelayCommand SubmitCommand {get; set;}
    public IAsyncRelayCommand AddNextIntervalCommand {get; set;}

    private bool _isSubmitButtonVisible;
    public bool IsSubmitButtonVisible
    {
        get => _isSubmitButtonVisible;
        set => SetProperty(ref _isSubmitButtonVisible, value);
    }

    private bool _isSecondarySubmitButtonVisible;
    public bool IsSecondarySubmitButtonVisible
    {
        get => _isSecondarySubmitButtonVisible;
        set => SetProperty(ref _isSecondarySubmitButtonVisible, value);
    }

    private bool _isEightIntervalsVisible;
    public bool IsEightIntervalsVisible
    {
        get => _isEightIntervalsVisible;
        set => SetProperty(ref _isEightIntervalsVisible, value);
    }

    private bool _isFourIntervalsVisible;
    public bool IsFourIntervalsVisible
    {
        get => _isFourIntervalsVisible;
        set => SetProperty(ref _isFourIntervalsVisible, value);
    }

    private bool isBlacVisible;
    public bool IsBlacVisible
    {
        get => isBlacVisible;
        set => SetProperty(ref isBlacVisible, value);
    }

    public IAsyncRelayCommand NavigateBackCommand =>
        new AsyncRelayCommand(_navigationService.NavigateBack);

    public EnterTestDataViewModel()
    {
        SubmitCommand = new AsyncRelayCommand(Submit);
        AddNextIntervalCommand = new AsyncRelayCommand(AddNextInterval);
        IsSubmitButtonVisible = true;
        IsSecondarySubmitButtonVisible = false;
        _intervals = new List<TestCalendarDetailsModel>();
        standardInterval = string.Empty;
        intervalTypeTitle = string.Empty;
    }

    public async Task Submit()
    {
        try
        {
            if (await PostLastInterval())
            {
                OpenLoadingView = true;

                if (CurrentTestSetup?.TestDetailAthleteId == null)
                {
                    await ShowMessageError("Current test setup is not properly initialized.");
                    return;
                }

                BepsTestDataModel? bepsTestDataModel = await ServiceHelper
                    .GetService<BepsTestDataService>()
                    .CreateBepsResult(CurrentTestSetup.TestDetailAthleteId.Value);

                if (bepsTestDataModel is null)
                {
                    await ShowMessageError();
                    return;
                }

                OpenLoadingView = false;
                await _navigationService.NavigateToPage<BepsScoresPage>(bepsTestDataModel);
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            OpenLoadingView = false;
            throw;
        }
    }

    private async Task<bool> PostLastInterval()
    {
        TestCalendarDetailsModel? currentInterval = Items.LastOrDefault();

        if (currentInterval != null && IsDistanceBasedInterval(currentInterval) && string.IsNullOrEmpty(currentInterval?.Time)
            || (currentInterval != null && IsTimeBasedInterval(currentInterval) && currentInterval.ActualPaceMph == 0)
            || (currentInterval != null && currentInterval.HeartRate == 0)
            || (currentInterval != null && IsBlacVisible && currentInterval.BLacValue == 0))
        {
            string message = "Please fill in all required fields.";
            await ShowToastMessage(message);
            return false;
        }

        if (currentInterval != null && currentInterval.HeartRate > 250)
        {
            string message = "Heart rate over 250 is invalid. Please enter a a value below 250.";
            await ShowToastMessage(message);
            return false;
        }

        if (currentInterval != null && currentInterval.BLacValue > 24)
        {
            string message = "BLac values over 24 are invalid. Please enter a value below 24.";
            await ShowToastMessage(message);
            return false;
        }

        if (Items.Count > 1)
        {
            if (currentInterval == null)
            {
                await ShowMessageError("Current interval is not properly initialized.");
                return false;
            }

            bool flowControl = await ValidateCurrentInterval(currentInterval);

            if (!flowControl)
                return false;
        }

        await PostCurrentTestDataEntry(currentInterval);
        SetNextInterval();
        return true;
    }

    private async Task AddNextInterval()
    {
        IsBusy = true;
        
        try
        {
            await PostLastInterval();
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task<bool> ValidateCurrentInterval(TestCalendarDetailsModel item)
    {
        int currentInterval = item.IntervalCount;

        TestCalendarDetailsModel? previousItem = Items
            .FirstOrDefault(x => x.IntervalCount == currentInterval - 1);

        if (previousItem is null)
        {
            await ShowMessageError();
            return false;
        }

        if (IsDistanceBasedInterval(item))
        {
            if (IsUserIncreasingTime(previousItem, currentItem: item))
            {
                string message = $"Time must be less than or equal to the Previous stage";
                await ShowToastMessage(message);
                return false;
            }
        }
        else if (IsTimeBasedInterval(item))
        {
            if (IsUserDecreasingPaceMph(previousItem, currentItem: item))
            {
                string message = "Speed must be greater than or equal to the Previous stage";
                await ShowToastMessage(message);
                return false;
            }
        }
        else
        {
            //Watts
        }

        if (IsUserDecreasingHeartRate(previousItem, currentItem: item))
        {
            string message = $"Heart Rate must be greater than or equal to the Previous stage";
            await ShowToastMessage(message);
            return false;
        }

        if (IsUserDecreasingBLac(previousItem, currentItem: item))
        {
            string message = $"BLac must be greater than or equal to the Previous stage";
            await ShowToastMessage(message);
            return false;
        }

        return true;
    }

    private bool IsDistanceBasedInterval(TestCalendarDetailsModel? item)
    {
        if (item == null || item.IntervalType == null)
        {
            return false;
        }

        return item.IntervalType.Value == IntervalTypeEnum.DistanceBased;
    }

    private bool IsTimeBasedInterval(TestCalendarDetailsModel item)
    {
        if (item is null)
        {
            return false;
        }

        if (item.IntervalType == null)
        {
            return false;
        }

        return item.IntervalType.Value == IntervalTypeEnum.TimeBased;
    }

    private bool IsUserDecreasingPaceMph(TestCalendarDetailsModel previousItem, TestCalendarDetailsModel currentItem)
    {
        return currentItem.ActualPaceMph < previousItem.ActualPaceMph;
    }

    private bool IsUserDecreasingBLac(TestCalendarDetailsModel previousItem, TestCalendarDetailsModel currentItem)
    {
        return currentItem.BLacValue < previousItem.BLacValue;
    }

    private bool IsUserDecreasingHeartRate(TestCalendarDetailsModel previousItem, TestCalendarDetailsModel currentItem)
    {
        return currentItem.HeartRate < previousItem.HeartRate;
    }

    private bool IsUserIncreasingTime(TestCalendarDetailsModel previousItem, TestCalendarDetailsModel currentItem)
    {
        TimeSpan previousTimeSpan = default;
        TimeSpan currentTimeSpan = default;

        if (TimeSpan.TryParse(previousItem.Time, out var previousTs))
        {
            previousTimeSpan = previousTs;
        }

        if (TimeSpan.TryParse(currentItem.Time, out var currentTs))
        {
            currentTimeSpan = currentTs;
        }

        return currentTimeSpan > previousTimeSpan;
    }

    private void SetNextInterval()
    {
        intervalsCount++;

        TestCalendarDetailsModel? testCalendarDTO = _intervals
            .FirstOrDefault(x => x.IntervalCount == intervalsCount);

        if (testCalendarDTO is null)
            return;

        Items.Add(testCalendarDTO);

        if (IsFourIntervalsVisible && intervalsCount == 4)
        {
            IsAddNextIntervalVisible = false;
        }
        else if (intervalsCount == 5)
        {
            IsSubmitButtonVisible = false;
            IsSecondarySubmitButtonVisible = true;
        }

        if (intervalsCount == TestProtocolConstants.MINIMUM_INTERVAL_COUNT)
        {
            IsSubmitButtonEnabled = true;
        }
        else if (_intervals.Max(x => x.IntervalCount) == intervalsCount)
        {
            IsAddNextIntervalVisible = false;
        }
    }

    private async Task PostCurrentTestDataEntry(TestCalendarDetailsModel? item)
    {
        int? minutes;
        int? seconds;

        if (item is null)
            return;

        if (item.IntervalType.HasValue && item.IntervalType.Value == IntervalTypeEnum.TimeBased)
        {
            (minutes, seconds) = CalculateTreadmillTime(item);
        }
        else
        {
            bool parsed = TimeSpan.TryParse($"00:{item.Time}", out var result);

            if (parsed)
            {
                minutes = result.Minutes;
                seconds = result.Seconds;
            }
            else
            {
                throw new Exception("Time was not parsed correctly!");
            }
        }

        if (CurrentTestSetup?.TestDetailAthleteId == null)
        {
            throw new InvalidOperationException("CurrentTestSetup or TestDetailAthleteId cannot be null.");
        }

        var testDataEntryDto = new TestDataEntryDto
        {
            TestDetailAthleteId = CurrentTestSetup.TestDetailAthleteId,
            Interval = item.IntervalCount,
            IntervalDistance = item.StandardInterval,
            IntervalTime = minutes,
            IntervalSecond = seconds,
            HeartRate = item.HeartRate,
            Blac = item.BLacValue
        };

        if (!IsBlacVisible)
        {
            switch (item.IntervalCount)
            {
                case 1:
                    testDataEntryDto.Blac = 3;
                    break;
                case 2:
                    testDataEntryDto.Blac = 5;
                    break;
                case 3:
                    testDataEntryDto.Blac = 7;
                    break;
                case 4:
                    testDataEntryDto.Blac = 9;
                    break;
            }
        }

        TestDataEntryModel? testDataEntryModel = await ServiceHelper
            .GetService<TestDataEntryService>()
            .SendTestDataEntry(testDataEntryDto);

        if (testDataEntryModel is null)
        {
            string message = $"An error has occured while posting the current interval: {item.IntervalCount}. Please try again later.";
            await ShowMessageError(message);
        }
    }

    private (int minutes, int seconds) CalculateTreadmillTime(TestCalendarDetailsModel? item)
    {
        if (item is null)
        {
            throw new ArgumentNullException(nameof(item), "Item cannot be null.");
        }

        double speedResult = (item?.ActualPaceMph ?? 0) * 1609.34 / 3600;

        double timeResult = item?.StandardInterval.HasValue == true && speedResult > 0
            ? item.StandardInterval.Value / speedResult
            : 0;

        TimeSpan ts = TimeSpan.FromSeconds(Math.Round(timeResult));
        return (ts.Minutes, ts.Seconds);
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();

        CurrentTestSetup = MyByaContext.Instance.CurrentTestSetup;
        TestCalendarModel? testCalendar = MyByaContext.Instance.CurrentTestCalendar;

        if (testCalendar is null || CurrentTestSetup is null)
        {
            await ShowMessageError();
            return;
        }

        SetHeartRateOnlyTest();
        SetHeartRateAndBlacTest();
        SetStandardInterval(testCalendar);
        SetIntervalTypeTitle(testCalendar);
        SetIntervals(testCalendar);
    }

    private void SetHeartRateAndBlacTest()
    {
        IsEightIntervalsVisible = CurrentTestSetup?.DataType == 1;
        IsBlacVisible = IsEightIntervalsVisible;
    }

    private void SetHeartRateOnlyTest()
    {
        IsFourIntervalsVisible = CurrentTestSetup?.DataType == 0;
    }

    private void SetIntervals(TestCalendarModel testCalendar)
    {
        for (int i = 0; i < TestProtocolConstants.NUMBER_OF_INTERVALS; i++)
        {
            var model = new TestCalendarDetailsModel
            {
                IntervalCount = i + 1,
                IntervalType = testCalendar.IntervalType.HasValue ? testCalendar.IntervalType.Value : default,
                StandardInterval = testCalendar.StandardInterval,
                IsBlacVisible = IsBlacVisible
            };

            model.SetActualPaceInputVisibility(model.IntervalType.Value);

            if (model.IntervalCount == 1)
            {
                intervalsCount++;
                Items.Add(model);
                continue;
            }

            _intervals ??= new List<TestCalendarDetailsModel>();
            _intervals.Add(model);
        }
    }

    private void SetIntervalTypeTitle(TestCalendarModel testCalendar)
    {
        IntervalTypeTitle = testCalendar.IntervalType.HasValue && testCalendar.IntervalType.Value == IntervalTypeEnum.DistanceBased
            ? "Actual Time"
            : "Actual Pace";
    }

    private void SetStandardInterval(TestCalendarModel testCalendar)
    {
        StandardInterval = testCalendar.StandardInterval.HasValue
            ? $"{testCalendar.StandardInterval.Value}m Stages"
            : "Unknown Stages";
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        CurrentTestSetup = (AthleteTestSetupModel?)parameter;
        return base.OnNavigatingTo(parameter);
    }
}
