using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
using CommunityToolkit.Maui.Core.Extensions;
using CommunityToolkit.Mvvm;
using CommunityToolkit.Mvvm.Input;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Parameters;
using MyBya.Services;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.ViewModels.TestProtocol
{
    public class TestProtocolInitialCreationViewModel : ViewModelBase
    {
        private List<TrainingPlanEventModel> trainingPlanEvents { get; set; } = new List<TrainingPlanEventModel>();

        private ObservableCollection<string> eventList = new ObservableCollection<string>();
        public ObservableCollection<string> EventList
        {
            get => eventList;
            set => SetProperty(ref eventList, value);
        }

        private SportEnum? selectedSport;
        public SportEnum? SelectedSport
        {
            get => selectedSport;
            set => SetProperty(ref selectedSport, value);
        }

        private bool isNextButtonEnabled;
        public bool IsNextButtonEnabled
        {
            get => isNextButtonEnabled;
            set => SetProperty(ref isNextButtonEnabled, value);
        }

        private LevelEnum? selectedLevel;
        public LevelEnum? SelectedLevel
        {
            get => selectedLevel;
            set => SetProperty(ref selectedLevel, value);
        }

        private string? selectedEvent;
        public string? SelectedEvent
        {
            get => selectedEvent;
            set => SetProperty(ref selectedEvent, value);
        }

        private bool isSelectedSportNotEmpty;
        public bool IsSelectedSportNotEmpty
        {
            get => isSelectedSportNotEmpty;
            set => SetProperty(ref isSelectedSportNotEmpty, value);
        }

        public IAsyncRelayCommand NextPhaseCommand { get; set; }

        public TestProtocolInitialCreationViewModel()
        {
            IsSelectedSportNotEmpty = false;
            NextPhaseCommand = new AsyncRelayCommand(NextPhase);
        }

        public override async Task OnNavigatingTo(object? parameter)
        {
            await base.OnNavigatingTo(parameter);

            if (parameter is SportEnum sport)
            {
                SelectedSport = sport;
                IsSelectedSportNotEmpty = true;
            }
        }

        private async Task NextPhase()
        {
            IsSelectedSportNotEmpty = SelectedSport != null;
            IsNextButtonEnabled = false;

            int? eventId = trainingPlanEvents
                .FirstOrDefault(x => x.EventName == SelectedEvent)?.Id;

            if (SelectedSport != null &&
                SelectedLevel != null &&
                !string.IsNullOrEmpty(SelectedEvent) &&
                eventId.HasValue)
            {
                var parameters = new TestProtocolCreationParameters(
                    (SportEnum)SelectedSport,
                    (LevelEnum)SelectedLevel,
                    SelectedEvent,
                    eventId.Value);

                await _navigationService.NavigateToPage<TestProtocolFinalCreationPage>(parameters);
            }
        }

        protected override void ViewModelBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            try
            {
                base.ViewModelBase_PropertyChanged(sender, e);

                if (e.PropertyName == nameof(SelectedSport) ||
                    e.PropertyName == nameof(SelectedLevel) ||
                    e.PropertyName == nameof(SelectedEvent))
                {
                    IsNextButtonEnabled = selectedSport != SportEnum.NONE &&
                        selectedLevel != LevelEnum.NONE &&
                        !string.IsNullOrEmpty(SelectedEvent);
                }

                if (e.PropertyName == nameof(SelectedSport) &&
                    e.PropertyName != nameof(SelectedLevel) &&
                    e.PropertyName != nameof(SelectedEvent) &&
                    selectedEvent is null)
                {
                    IsNextButtonEnabled = selectedSport != SportEnum.NONE;
                }

                if (e.PropertyName == nameof(SelectedSport) && selectedSport != SportEnum.NONE)
                {
                    if (trainingPlanEvents != null)
                    {
                        EventList = trainingPlanEvents
                            .Where(x => SelectedSport.HasValue && x.Sport == (int)SelectedSport.Value && x.EventName != null)
                            .Select(x => x.EventName!)
                            .ToObservableCollection();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, ex.Message);
                throw;
            }
        }

        public override async Task OnAppearing()
        {
            await base.OnAppearing();

            IsBusy = true;

            trainingPlanEvents = await ServiceHelper
                .GetService<TrainingPlanEventService>()
                .GetTrainingPlanEvents();

            IsBusy = false;
        }
    }
}

