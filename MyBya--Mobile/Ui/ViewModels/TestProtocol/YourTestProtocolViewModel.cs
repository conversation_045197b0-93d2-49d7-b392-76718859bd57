using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Services;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels.TestProtocol
{
    public class YourTestProtocolViewModel : ViewModelBase
    {
        private TestProtocolParameters _testProtocolParameters;

        private string _sportAndEvent;
        public string SportAndEvent
        {
            get => _sportAndEvent;
            set => SetProperty(ref _sportAndEvent, value);
        }

        private string _level;
        public string Level
        {
            get => _level;
            set => SetProperty(ref _level, value);
        }

        private string _raceGoal;
        public string RaceGoal
        {
            get => _raceGoal;
            set => SetProperty(ref _raceGoal, value);
        }

        private string _testLocation;
        public string TestLocation
        {
            get => _testLocation;
            set => SetProperty(ref _testLocation, value);
        }

        private string _dataCapture;
        public string DataCapture
        {
            get => _dataCapture;
            set => SetProperty(ref _dataCapture, value);
        }

        private TestProtocolInstructionModel testProtocolInstruction;
        public TestProtocolInstructionModel TestProtocolInstruction
        {
            get => testProtocolInstruction;
            set => SetProperty(ref testProtocolInstruction, value);
        }

        public IAsyncRelayCommand NavigateToEnterTestDataCommand { get; private set; }
        public IAsyncRelayCommand EditTestingInputCommand { get; set; }

        public YourTestProtocolViewModel()
        {
            _testProtocolParameters = new TestProtocolParameters();
            _sportAndEvent = string.Empty;
            _level = string.Empty;
            _raceGoal = string.Empty;
            _testLocation = string.Empty;
            _dataCapture = string.Empty;
            testProtocolInstruction = new TestProtocolInstructionModel();
            NavigateToEnterTestDataCommand = new AsyncRelayCommand(Navigate);
            EditTestingInputCommand = new AsyncRelayCommand(_navigationService.NavigateBack);
        }

        private async Task Navigate()
        {
            await _navigationService.NavigateToPage<EnterTestDataPage>();
        }

        public override Task OnNavigatingTo(object? parameter)
        {
            if (parameter == null)
                return base.OnNavigatingTo(parameter);

            if (parameter is TestProtocolParameters testProtocolParameters)
            {
                _testProtocolParameters = testProtocolParameters;

                SportAndEvent = $"{_testProtocolParameters.Sport}, {_testProtocolParameters.Event}";
                RaceGoal = _testProtocolParameters.Time ?? string.Empty;
                TestLocation = _testProtocolParameters.IsTreadmill
                    ? RunningModeEnum.TREADMILL.ToString()
                    : RunningModeEnum.TRACK.ToString();
                DataCapture = _testProtocolParameters.IsHeartRateAndLactate
                    ? "Heart Rate and Lactate levels"
                    : "Heart Rate only";
            }

            return base.OnNavigatingTo(parameter);
        }

        public override async Task OnAppearing()
        {
            await base.OnAppearing();

            if (_testProtocolParameters is null)
                return;

            int sport = (int)_testProtocolParameters.Sport;
            int interval_type = (int)_testProtocolParameters.IntervalType;
            int interval_distance = _testProtocolParameters.IntervalDistance;

            List<TestProtocolInstructionModel> instructons = await ServiceHelper
                .GetService<TestProtocolInstructionService>()
                .GetTestProtocolInstructions(sport, interval_type, interval_distance);

            if (instructons != null && instructons.Count > 0)
                TestProtocolInstruction = instructons[0];
        }
    }
}
