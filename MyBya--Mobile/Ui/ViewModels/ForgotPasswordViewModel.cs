using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MyBya.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;

namespace MyBya.Ui.ViewModels;

public partial class ForgotPasswordViewModel : ObservableValidator
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<ForgotPasswordViewModel> _logger;

    [ObservableProperty]
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    private string email = string.Empty;

    [ObservableProperty]
    private bool isLoading = false;

    [ObservableProperty]
    private string message = string.Empty;

    [ObservableProperty]
    private bool isSuccess = false;

    public ForgotPasswordViewModel(IAuthenticationService authenticationService, ILogger<ForgotPasswordViewModel> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    [RelayCommand]
    private async Task SendResetEmailAsync()
    {
        try
        {
            IsLoading = true;
            Message = string.Empty;

            if (string.IsNullOrWhiteSpace(Email))
            {
                Message = "Please enter your email address";
                IsSuccess = false;
                return;
            }

            if (!IsValidEmail(Email))
            {
                Message = "Please enter a valid email address";
                IsSuccess = false;
                return;
            }

            var success = await _authenticationService.ForgotPasswordAsync(Email);

            if (success)
            {
                Message = "Password reset instructions have been sent to your email address";
                IsSuccess = true;
            }
            else
            {
                Message = "Failed to send reset email. Please try again";
                IsSuccess = false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during password reset");
            Message = "An error occurred. Please try again";
            IsSuccess = false;
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task NavigateToLoginAsync()
    {
        await Shell.Current.GoToAsync("..");
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
