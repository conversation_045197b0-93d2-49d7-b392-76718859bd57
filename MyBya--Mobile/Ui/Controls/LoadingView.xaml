<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MyBya.Ui.Controls.LoadingView"
             xmlns:views="clr-namespace:MyBya.Ui.Views"
             Title="LoadingView"
             BackgroundColor="#352859">
        <Grid
            RowDefinitions="*">
            <StackLayout
                VerticalOptions="Fill">
                <StackLayout.Background>
                    <LinearGradientBrush>
                        <GradientStop Color="#483778" Offset="0.44"/>
                        <GradientStop Color="#231B3B" Offset="1.0"/>
                    </LinearGradientBrush>
                </StackLayout.Background>
                <Label
                    Margin="20,20,0,0"
                    FontSize="18"
                    Text="Analyzing..."
                    TextColor="White"/>
            </StackLayout>
        </Grid>
</ContentPage>