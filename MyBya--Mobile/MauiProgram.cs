﻿using CommunityToolkit.Maui;
using Microsoft.Extensions.Logging;
using MyBya.Configuration;
using MyBya.DbContext.Handler;
using MyBya.DbContext.Handler.Interface;
using MyBya.Extensions;
using MyBya.Helpers;
using Serilog;
using Syncfusion.Maui.Toolkit.Hosting;

#if IOS
using UIKit;
using Microsoft.Maui.Handlers;
#endif

namespace MyBya;

public static class MauiProgram
{
	public static MauiApp CreateMauiApp()
	{
        CreateUnhandledExceptionsSetup();
		return Builder();
	}


	private static void CreateUnhandledExceptionsSetup()
	{
		TaskScheduler.UnobservedTaskException += (s, e) =>
		{
			Log.Logger.Fatal(e.Exception, e.Exception.Message);
			e.SetObserved();
		};

		AppDomain.CurrentDomain.UnhandledException += (s, e) =>
		{
			if (e.ExceptionObject is Exception ex)
				Log.Logger.Fatal(ex, ex.Message);
    	};
  	}

	private static MauiApp Builder()
	{
		var builder = AppMauiContext.Instance.Builder;

        if (builder == null)
        {
            throw new InvalidOperationException("MauiAppBuilder is not initialized.");
        }

		builder
			.UseMauiApp<App>()
			.UseMauiCommunityToolkit()
			.UseMauiCommunityToolkitMediaElement()
			.ConfigureSyncfusionToolkit()
            .AddDependencies()
			.ConfigureFonts(fonts =>
			{
				fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
				fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
			});
			
#if IOS
		Microsoft.Maui.Handlers.EntryHandler.Mapper.AppendToMapping("CustomDoneButton", (handler, view) =>
		{
			if (handler.PlatformView is UITextField textField)
			{
				var toolbar = new UIToolbar
				{
					BarStyle = UIBarStyle.Default,
					Translucent = true,
				};
				toolbar.SizeToFit();

				var doneButton = new UIBarButtonItem(UIBarButtonSystemItem.Done, (s, e) =>
				{
					textField.ResignFirstResponder();
				});

				toolbar.SetItems(new[] { new UIBarButtonItem(UIBarButtonSystemItem.FlexibleSpace), doneButton }, false);
				textField.InputAccessoryView = toolbar;
			}
		});
#endif

#if DEBUG
		builder.Logging.AddDebug();
#endif
		return builder.Build();
	}
}
