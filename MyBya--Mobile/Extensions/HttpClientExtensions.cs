using System;
using System.Net.Http.Headers;

namespace MyBya.Extensions;

public static class HttpClientExtensions
{
    public static HttpClient SetBearerToken(this HttpClient client, string token)
    {
        if (client == null)
            throw new ArgumentNullException(nameof(client));

        if (string.IsNullOrWhiteSpace(token))
            return client;

        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        return client;
    }

    public static bool HasValidBearerToken(this HttpClient client)
    {
        return client.DefaultRequestHeaders.Authorization?.Scheme == "Bearer"
            && !string.IsNullOrWhiteSpace(client.DefaultRequestHeaders.Authorization.Parameter);
    }
}
