using MyBya.Enums;

namespace MyBya.Models;

public class TestCalendarDetailsModel : TestCalendarModel
{
    public int IntervalCount { get; set; }
    public bool MinutesAndSecondsInputsVisible { get; set; }
    public bool MPHInputVisible { get; set; }

    private bool isIntervalVisible;
    public bool IsIntervalVisible
    {
        get => isIntervalVisible;
        set => SetProperty(ref isIntervalVisible, value);
    }

    private int actualTimeMinutes;
    public int ActualTimeMinutes
    {
        get => actualTimeMinutes;
        set => SetProperty(ref actualTimeMinutes, value);
    }

    private int actualTimeSeconds;
    public int ActualTimeSeconds
    {
        get => actualTimeSeconds;
        set => SetProperty(ref actualTimeSeconds, value);
    }

    private double? actualPaceMph;
    public double? ActualPaceMph
    {
        get => actualPaceMph;
        set => SetProperty(ref actualPaceMph, value);
    }

    private int? heartRate;
    public int? HeartRate
    {
        get => heartRate;
        set => SetProperty(ref heartRate, value);
    }


    private double? bLacValue;
    public double? BLacValue
    {
        get => bLacValue;
        set => SetProperty(ref bLacValue, value);
    }

    private string time = string.Empty;
    public string Time
    {
        get => time;
        set => SetProperty(ref time, value);
    }

    private bool isBlacVisible;
    public bool IsBlacVisible
    {
        get => isBlacVisible;
        set => SetProperty(ref isBlacVisible, value);
    }

    public void SetActualPaceInputVisibility(IntervalTypeEnum intervalType)
    {
        MinutesAndSecondsInputsVisible = intervalType == IntervalTypeEnum.DistanceBased;
        MPHInputVisible = intervalType == IntervalTypeEnum.TimeBased;
    }
}
