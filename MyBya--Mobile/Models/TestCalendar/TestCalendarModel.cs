using System;
using System.Text.Json.Serialization;
using MyBya.Enums;
using MyBya.Models.Common;

namespace MyBya.Models;

public class TestCalendarModel : BaseModel
{
    public DateTime? CreationTime { get; set; }
    public Guid? CreatorId { get; set; }
    public DateTime? LastModificationTime { get; set; }
    public Guid? LastModifierId { get; set; }
    public bool? IsDeleted { get; set; }
    public Guid? DeleterId { get; set; }
    public DateTime? DeletionTime { get; set; }
    public DateTime? Date { get; set; }
    public int TimeIndicator { get; set; }
    public string? Location { get; set; }
    public SportEnum? Sport { get; set; }
    public ProtocolEnum? Protocol { get; set; }
    public IntervalTypeEnum? IntervalType { get; set; }
    public int? NumberOfInterval { get; set; }
    public int? SbtOwnerId { get; set; }
    public int? StandardInterval { get; set; }
}