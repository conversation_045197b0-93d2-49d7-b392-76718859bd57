﻿using System;
using System.Text.Json.Serialization;
using MyBya.Models.Common;

namespace MyBya.Models
{
	public class TestResultAnrcModel : BaseModel
	{
        public int? TestDetailAthleteId { get; set; }
        public string? System { get; set; }
        public string? Name { get; set; }
        public string? Anrc1 { get; set; }
        public string? Anrc2 { get; set; }
        public string? Anrc1Min { get; set; }
        public string? Anrc1Max { get; set; }
        public string? Anrc2Min { get; set; }
        public string? Anrc2Max { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}