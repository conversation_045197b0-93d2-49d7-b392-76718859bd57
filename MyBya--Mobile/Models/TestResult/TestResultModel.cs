using MyBya.Models.Common;

namespace MyBya.Models;

public class TestResultModel : BaseModel
{
    public int? TestDetailAthleteId { get; set; }
    public double? Af { get; set; }
    public double? Pac { get; set; }
    public double? Ltcc { get; set; }
    public double? Arc3 { get; set; }
    public double? Arc2 { get; set; }
    public double? Arc1 { get; set; }
    public double? Anrc2 { get; set; }
    public double? Anrc1 { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
