﻿using MyBya.Models.Common;

namespace MyBya.Models
{
	public class TestResultPaceChartModel : BaseModel
	{
        public int? TestDetailAthleteId { get; set; }
        public int? Sport { get; set; }
        public string? System { get; set; }
        public string? Arc1 { get; set; }
        public string? Arc2 { get; set; }
        public string? Arc3 { get; set; }
        public string? Ltcc { get; set; }
        public string? Arc1Min { get; set; }
        public string? Arc1Max { get; set; }
        public string? Arc2Min { get; set; }
        public string? Arc2Max { get; set; }
        public string? Arc3Min { get; set; }
        public string? Arc3Max { get; set; }
        public string? LtccMin { get; set; }
        public string? LtccMax { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? Data { get; set; }
    }
}

