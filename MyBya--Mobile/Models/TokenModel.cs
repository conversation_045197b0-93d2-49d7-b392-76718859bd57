using System;
using System.Text.Json.Serialization;
using MyBya.Models.Common;

namespace MyBya.Models;

public class TokenModel : BaseModel
{
    [JsonPropertyName("access_token")]
    public required string AccessToken { get; set; }

    [JsonPropertyName("token_type")]
    public required string TokenType { get; set; }

    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }

    [JsonPropertyName("refresh_token")]
    public required string RefreshToken { get; set; }
}
