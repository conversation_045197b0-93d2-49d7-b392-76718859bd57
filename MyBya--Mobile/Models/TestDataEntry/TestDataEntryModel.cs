using System;
using System.Text.Json.Serialization;
using MyBya.Models.Common;

namespace MyBya.Models;

public class TestDataEntryModel : BaseModel
{
    public DateTime? CreationTime { get; set; }
    public string? CreatorId { get; set; }
    public DateTime? LastModificationTime { get; set; }
    public string? LastModifierId { get; set; }
    public bool? IsDeleted { get; set; }
    public string? DeleterId { get; set; }
    public DateTime? DeletionTime { get; set; }
    public int? TestDetailAthleteId { get; set; }
    public int? Interval { get; set; }
    public int? IntervalDistance { get; set; }
    public int? IntervalTime { get; set; }
    public int? IntervalSecond { get; set; }
    public int? HeartRate { get; set; }
    public double? Blac { get; set; }
    public int? IntervalWatts { get; set; }
}