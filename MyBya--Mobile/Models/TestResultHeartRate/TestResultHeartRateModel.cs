using MyBya.Models.Common;

namespace MyBya.Models;

public class TestResultHeartRateModel : BaseModel
{
    public int AthleteTestSetupId { get; set; }
    public string? Zone1 { get; set; }
    public string? Zone2Low { get; set; }
    public string? Zone2Mid { get; set; }
    public string? Zone2High { get; set; }
    public string? Zone3 { get; set; }
    public int? Zone3Mid { get; set; }
    public int? TestDetailAthleteId { get; set; }
    public int? Zone1Max { get; set; }
    public int? Zone2Min { get; set; }
    public int? Zone2Max { get; set; }
    public int? Zone3Min { get; set; }
    public int? Zone3Max { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
