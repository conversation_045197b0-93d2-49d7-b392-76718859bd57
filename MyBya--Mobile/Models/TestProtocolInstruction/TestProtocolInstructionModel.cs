using System;
using MyBya.Models.Common;

namespace MyBya.Models;

public class TestProtocolInstructionModel : BaseModel
{
    public int? Sport { get; set; }
    public int? IntervalType { get; set; }
    public int? IntervalDistance { get; set; }
    public string? DistanceTimeInstructions { get; set; }
    public string? StagesInstructions { get; set; }
    public string? StartInstructions { get; set; }
    public string? EffortInstructions { get; set; }
    public string? FinishInstructions { get; set; }
    public string? RecordInstructions { get; set; }
}
