using System;
using AutoMapper;
using MyBya.DataAccess.Interfaces;
using MyBya.Entities;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Services;
using Serilog;

namespace MyBya.Shared;

public class HeartRateProcessingShared
{
    public static async Task SetHeartRates(AthleteTestSetupEntity athleteTestSetupEntity)
    {
        if (athleteTestSetupEntity == null)
            throw new ArgumentNullException(nameof(athleteTestSetupEntity));

        TestResultHeartRateModel? heartRatesResult = await ServiceHelper
            .GetService<TestResultHeartRateService>()
            .GetTestResultHeartRate(athleteTestSetupEntity.TestDetailAthleteId ?? 0);

        if (heartRatesResult is null)
            return;

        await SetHeartRatesLocally(heartRatesResult, athleteTestSetupEntity);
    }

    public static async Task SetHeartRatesLocally(TestResultHeartRateModel heartRates, AthleteTestSetupEntity athleteTestSetup)
    {
        ArgumentNullException.ThrowIfNull(heartRates);
        ArgumentNullException.ThrowIfNull(athleteTestSetup);

        try
        {
            int lowFirst = heartRates.Zone2Min ?? 0;
            int lowLast = lowFirst + 6;
            int midFirst = lowLast + 1;
            int midLast = midFirst + 6;
            int highFirst = midLast + 1;
            int highLast = highFirst + 6;

            heartRates.Zone1 = heartRates.Zone1Max.HasValue
                ? heartRates.Zone1Max.Value.ToString()
                : string.Empty;

            heartRates.Zone2Low = $"{lowFirst} - {lowLast}";
            heartRates.Zone2Mid = $"{midFirst} - {midLast}";
            heartRates.Zone2High = $"{highFirst} - {highLast}";
            heartRates.Zone3 = $"{heartRates.Zone3Min ?? 0} - {heartRates.Zone3Max ?? 0}";

            var entity = ServiceHelper
                .GetService<IMapper>()
                .Map<TestResultHeartRatesEntity>(heartRates);

            entity.AthleteTestSetupId = athleteTestSetup.Id;
            entity.Sport = athleteTestSetup.Sport ?? 0;

            var testResultHeartRateDataAccess = ServiceHelper
                .GetService<ITestResultHeartRateRepository>();

            var items = await testResultHeartRateDataAccess
                .SelectByCriteria(x => x.Sport == entity.Sport);

            if (items?.Any() == true)
            {
                var item = items.First();

                if (item != null)
                {
                    entity.IdHeartRate = item.IdHeartRate;
                    await testResultHeartRateDataAccess.Update(entity);
                    return;
                }
            }

            await testResultHeartRateDataAccess.Insert(entity);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error setting heart rates locally.");
        }
    }
}
