using System;

namespace MyBya.Interfaces;

public interface INavigationService
{
    Task NavigateBack();
    Task NavigateToPage<T>(object? parameter = null) where T : Page;
    Task NavigateToModal<T>(object? parameter = null) where T : Page;
    Task CloseModal();
    Task CloseAll();
    Task CloseCurrentAndNavigateToModal<T>(object? parameter = null) where T : Page;
    Task CloseCurrentAndNavigateToPage<T>(object? parameter = null) where T : Page;
}
