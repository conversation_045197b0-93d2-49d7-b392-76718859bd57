﻿using MyBya.Entities.Common;
using SQLite;

namespace MyBya.Entities
{
    [Table("test_result_pace_chart")]
	public class TestResultPaceChartEntity : BaseEntity
    {
        [PrimaryKey, AutoIncrement]
        public int IdPace { get; set; }
        public int Sport { get; set; }
        public int? TestDetailAthleteId { get; set; }
        public int AthleteTestSetupId { get; set; }
        public string? System { get; set; }
        public string? Arc1 { get; set; }
        public string? Arc2 { get; set; }
        public string? Arc3 { get; set; }
        public string? Ltcc { get; set; }
        public string? Arc1Min { get; set; }
        public string? Arc1Max { get; set; }
        public string? Arc2Min { get; set; }
        public string? Arc2Max { get; set; }
        public string? Arc3Min { get; set; }
        public string? Arc3Max { get; set; }
        public string? LtccMin { get; set; }
        public string? LtccMax { get; set; }
    }
}
