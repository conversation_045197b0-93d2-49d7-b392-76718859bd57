using System;
using MyBya.Entities.Common;
using SQLite;

namespace MyBya.Entities;

[Table("athlete_test_setup")]
public class AthleteTestSetupEntity : BaseEntity
{
    [PrimaryKey, AutoIncrement]
    public int TestId { get; set; } // Primary key for AthleteTestSetupEntity
    public int Id { get; set; } // Actual AthleteTestSetup Id
    public DateTime? CreationTime { get; set; }
    public Guid? CreatorId { get; set; }
    public DateTime? LastModificationTime { get; set; }
    public Guid? LastModifierId { get; set; }
    public int? TestDetailAthleteId { get; set; }
    public int? TestCalendarId { get; set; }
    public int? Sport { get; set; }
    public string? Time { get; set; }
    public int? DataType { get; set; }
    public int? IntervalType { get; set; }
    public int? Level { get; set; }
    public int? TrainingPlanEventId { get; set; }
    public int? Systems { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
