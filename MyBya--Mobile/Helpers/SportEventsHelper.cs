namespace MyBya.Helpers;

using MyBya.Constants;
using MyBya.Enums;

public static class SportEventsHelper
{
    private static List<string> GetSwimmingEvents()
    {
        return new List<string>
        {
            SwimmingEventConstants.OPEN_WATER_SHORT,
            SwimmingEventConstants.OPEN_WATER_MEDIUM,
            SwimmingEventConstants.OPEN_WATER_LONG
        };
    }

    private static List<string> GetRunningEvents()
    {
        return new List<string>
        {
            RunningEventConstants.RUNNING_400_800,
            RunningEventConstants.RUNNING_800_1600,
            RunningEventConstants.RUNNING_1600_3200,
            RunningEventConstants.CROSS_COUNTRY_5K,
            RunningEventConstants.RUNNING_5K,
            RunningEventConstants.RUNNING_10K,
            RunningEventConstants.HALF_MARATHON,
            RunningEventConstants.MARATHON
        };
    }

    private static List<string> GetCyclingEvents()
    {
        return new List<string>
        {
            CyclingEventConstants.MTB_XC_SHORT_TRACK,
            CyclingEventConstants.CRITS_AND_ROAD,
            CyclingEventConstants.ULTRA_AND_GRAVEL
        };
    }

    private static List<string> GetRowingEvents()
    {
        return new List<string>
        {
            RowingEventConstants.TWO_KM,
            RowingEventConstants.HEAD_RACING
        };
    }

    public static List<string> GetEventBySport(SportEnum sport)
    {
        return sport switch
        {
            SportEnum.SWIMMING => GetSwimmingEvents(),
            SportEnum.RUNNING => GetRunningEvents(),
            SportEnum.CYCLING => GetCyclingEvents(),
            SportEnum.ROWING => GetRowingEvents(),
            _ => new List<string>()
        };
    }
}
