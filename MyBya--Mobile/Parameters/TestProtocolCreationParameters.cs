using MyBya.Enums;

namespace MyBya.Parameters
{
    public class TestProtocolCreationParameters
    {
        public SportEnum Sport { get; set; }
        public LevelEnum Level { get; set; }
        public string Event { get; set; }
        public int EventId { get; set; }

        public TestProtocolCreationParameters(SportEnum sport, LevelEnum level, string @event, int eventId)
        {
            Sport = sport;
            Level = level;
            Event = @event;
            EventId = eventId;
        }
    }
}
