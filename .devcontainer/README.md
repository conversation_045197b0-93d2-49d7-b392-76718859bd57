# MyBya MAUI Development Container

This devcontainer provides a complete development environment for the MyBya .NET MAUI mobile application with full Android development capabilities.

## 🚀 Features

### Base Environment
- **Base Image**: `mcr.microsoft.com/dotnet/sdk:8.0`
- **.NET SDK**: Version 8.0 (matching your project requirements)
- **Operating System**: Ubuntu Linux

### Android Development
- **Android SDK**: Latest version with command-line tools
- **Android API Levels**: 21-34 (covering your minimum API 21 requirement)
- **Build Tools**: Latest stable versions (34.0.0, 33.0.2)
- **Platform Tools**: Latest version including ADB
- **Android Emulator**: System images for testing

### Java Development Kit
- **OpenJDK 17**: Required for Android development
- **Environment Variables**: Properly configured JAVA_HOME

### .NET MAUI Workload
- **MAUI Workload**: Installed via `dotnet workload install maui`
- **Additional Workloads**: Android, iOS, macCatalyst
- **Auto-updates**: Workloads are updated during container creation

### VS Code Extensions
- **C# Dev Kit**: Complete C# development experience
- **.NET MAUI Extension**: MAUI-specific tooling
- **XML Support**: For XAML editing
- **Auto Rename Tag**: Helpful for XAML development
- **GitHub Copilot**: AI-powered code assistance (if available)

## 🛠️ Environment Variables

The following environment variables are automatically configured:

```bash
ANDROID_HOME=/opt/android-sdk
ANDROID_SDK_ROOT=/opt/android-sdk
JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$ANDROID_HOME/build-tools/34.0.0
```

## 📱 Building Your MAUI App

### For Android
```bash
# Build for Android
dotnet build -f net8.0-android

# Build and package APK
dotnet publish -f net8.0-android -c Release

# Build specific configuration
dotnet build -f net8.0-android -c Debug
```

### Verify Installation
```bash
# Check .NET version
dotnet --version

# List installed workloads
dotnet workload list

# Check Android SDK
sdkmanager --list_installed

# Verify ADB
adb --version
```

## 🔧 Development Workflow

1. **Open in VS Code**: Use "Reopen in Container" when prompted
2. **Wait for Setup**: The container will automatically install all dependencies
3. **Start Developing**: All tools are ready for MAUI development
4. **Build & Test**: Use the integrated terminal for build commands

## 📂 Volume Mounts

- **Android Config**: `.android` folder is mounted to persist Android SDK settings
- **Source Code**: Your workspace is mounted at `/workspace`

## 🚪 Port Forwarding

The following ports are automatically forwarded:
- **5000, 5001**: Common .NET development ports
- **8080**: Alternative web development port

## 🐛 Troubleshooting

### Android SDK Issues
```bash
# Refresh Android SDK licenses
yes | sdkmanager --licenses

# Update Android SDK
sdkmanager --update
```

### .NET MAUI Issues
```bash
# Update MAUI workload
dotnet workload update

# Reinstall MAUI workload
dotnet workload uninstall maui
dotnet workload install maui
```

### Container Rebuild
If you encounter issues, try rebuilding the container:
1. Open Command Palette (Ctrl+Shift+P)
2. Run "Dev Containers: Rebuild Container"

## 📋 System Requirements

- **Docker**: Installed and running
- **VS Code**: With Dev Containers extension
- **Memory**: At least 8GB RAM recommended
- **Storage**: At least 10GB free space for Android SDK

## 🔄 Updates

To update the development environment:
1. Pull latest changes to the devcontainer configuration
2. Rebuild the container using VS Code Command Palette
3. The setup script will install the latest SDK components

## 📞 Support

If you encounter issues with the devcontainer setup:
1. Check the setup logs in the VS Code terminal
2. Verify Docker is running and has sufficient resources
3. Try rebuilding the container
4. Check the troubleshooting section above
