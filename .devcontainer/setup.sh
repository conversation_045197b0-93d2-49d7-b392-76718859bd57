#!/bin/bash

set -e

echo "🚀 Setting up .NET MAUI Development Environment..."

# Update package lists
sudo apt-get update

# Install essential packages
sudo apt-get install -y \
    wget \
    unzip \
    curl \
    git \
    build-essential \
    libc6-dev \
    libgcc-s1 \
    libgssapi-krb5-2 \
    libicu-dev \
    libssl-dev \
    libstdc++6 \
    zlib1g \
    ca-certificates \
    libc6 \
    libgcc1 \
    libgssapi-krb5-2 \
    libstdc++6 \
    zlib1g


# fix aapt2 bug
sudo dpkg --add-architecture amd64
sudo apt update
sudo apt install libc6:amd64

# Install .NET 8 SDK alongside .NET 9
curl -fsSL https://dot.net/v1/dotnet-install.sh | bash /dev/stdin --channel 8.0

# Install dotnet-gitversion tool globally
dotnet tool install --global GitVersion.Tool

# Create Android SDK directory with proper permissions
sudo mkdir -p /opt/android-sdk
sudo chown -R $USER:$USER /opt/android-sdk
cd /opt/android-sdk

# Download and install Android command line tools
echo "📱 Installing Android SDK Command Line Tools..."
wget -q https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip
unzip -q commandlinetools-linux-11076708_latest.zip
rm commandlinetools-linux-11076708_latest.zip

# Create proper directory structure
mkdir -p cmdline-tools/latest
mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null || true
rmdir cmdline-tools/bin cmdline-tools/lib 2>/dev/null || true

# Set environment variables for this session
export ANDROID_HOME=/opt/android-sdk
export ANDROID_SDK_ROOT=/opt/android-sdk
export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools

# Accept Android SDK licenses
echo "📋 Accepting Android SDK licenses..."
yes | sdkmanager --licenses > /dev/null 2>&1

# Install Android SDK components
echo "⬇️  Installing Android SDK components..."
sdkmanager --install \
    "platform-tools" \
    "build-tools;34.0.0" \
    "platforms;android-34" 

# Set proper permissions
sudo chmod -R 755 /opt/android-sdk
sudo chown -R $USER:$USER /opt/android-sdk

# Install .NET MAUI workload
echo "🔧 Installing .NET MAUI workload..."
sudo dotnet workload update
sudo dotnet workload install maui

# Install additional .NET workloads that might be useful
dotnet workload install android ios maccatalyst

# Verify installations
echo "✅ Verifying installations..."
echo "📱 Android SDK location: $ANDROID_HOME"
echo "📱 Android SDK version:"
sdkmanager --version

echo "🔧 .NET version:"
dotnet --version

echo "🔧 .NET workloads installed:"
dotnet workload list

echo "📱 Android platform tools:"
adb --version

echo "🎉 Setup complete! Your .NET MAUI development environment is ready."
echo ""
echo "📋 Environment Summary:"
echo "  - .NET SDK: $(dotnet --version)"
echo "  - Android SDK: $ANDROID_HOME"
echo "  - Java: $JAVA_HOME"
echo "  - MAUI workload: Installed"
echo ""
echo "🚀 You can now build and develop your MAUI application!"
