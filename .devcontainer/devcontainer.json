{"name": "MyBya MAUI Development", "image": "mcr.microsoft.com/devcontainers/dotnet:1-9.0-bookworm", "features": {"ghcr.io/devcontainers/features/java:1": {"version": "17", "jdkDistro": "ms"}, "ghcr.io/devcontainers/features/node:1": {"version": "lts"}, "ghcr.io/devcontainers/features/common-utils:2": {"installZsh": true, "configureZshAsDefaultShell": true, "installOhMyZsh": true}, "ghcr.io/devcontainers/features/git:1": {}}, "customizations": {"vscode": {"extensions": ["ms-dotnettools.csharp", "ms-dotnettools.csdevkit", "ms-dotnettools.vscode-dotnet-runtime", "ms-dotnettools.dotnet-maui", "ms-vscode.vscode-json", "ms-vscode.xml", "ms-dotnettools.vscode-dotnet-runtime", "ms-dotnettools.vscodeintellicode-csharp"], "settings": {"dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "dotnet.server.useOmnisharp": false, "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true}}}, "containerEnv": {"ANDROID_HOME": "/opt/android-sdk", "ANDROID_SDK_ROOT": "/opt/android-sdk", "JAVA_HOME": "/usr/lib/jvm/msopenjdk-17"}, "remoteEnv": {"PATH": "${containerEnv:PATH}:${containerEnv:ANDROID_HOME}/cmdline-tools/latest/bin:${containerEnv:ANDROID_HOME}/platform-tools:${containerEnv:ANDROID_HOME}/build-tools/34.0.0", "GITHUB_USERNAME": "${localEnv:GITHUB_USERNAME}", "GITHUB_TOKEN": "${localEnv:GITHUB_TOKEN}", "GIT_USER_NAME": "${localEnv:GIT_USER_NAME}", "GIT_USER_EMAIL": "${localEnv:GIT_USER_EMAIL}"}, "postCreateCommand": "bash .devcontainer/setup.sh", "remoteUser": "vscode", "containerUser": "vscode", "forwardPorts": [5000, 5001, 8080], "mounts": ["source=${localEnv:HOME}/.android,target=/root/.android,type=bind,consistency=cached"], "runArgs": ["--privileged"]}