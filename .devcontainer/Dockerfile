# Use the official .NET SDK image as base
FROM mcr.microsoft.com/dotnet/sdk:8.0

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    unzip \
    curl \
    git \
    build-essential \
    libc6-dev \
    libgcc-s1 \
    libgssapi-krb5-2 \
    libicu-dev \
    libssl-dev \
    libstdc++6 \
    zlib1g \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install OpenJDK 17 (required for Android development)
RUN apt-get update && apt-get install -y openjdk-17-jdk && rm -rf /var/lib/apt/lists/*

# Set Java environment variables
ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
ENV PATH=$PATH:$JAVA_HOME/bin

# Set Android environment variables
ENV ANDROID_HOME=/opt/android-sdk
ENV ANDROID_SDK_ROOT=/opt/android-sdk
ENV PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$ANDROID_HOME/build-tools/34.0.0

# Create Android SDK directory
RUN mkdir -p $ANDROID_HOME

# Download and install Android command line tools
WORKDIR $ANDROID_HOME
RUN wget -q https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip \
    && unzip -q commandlinetools-linux-11076708_latest.zip \
    && rm commandlinetools-linux-11076708_latest.zip \
    && mkdir -p cmdline-tools/latest \
    && mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null || true

# Accept Android SDK licenses and install components
RUN yes | $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --licenses > /dev/null 2>&1 \
    && $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --install \
        "platform-tools" \
        "build-tools;34.0.0" \
        "build-tools;33.0.2" \
        "platforms;android-34" \
        "platforms;android-33" \
        "platforms;android-32" \
        "platforms;android-31" \
        "platforms;android-30" \
        "platforms;android-29" \
        "platforms;android-28" \
        "platforms;android-27" \
        "platforms;android-26" \
        "platforms;android-25" \
        "platforms;android-24" \
        "platforms;android-23" \
        "platforms;android-22" \
        "platforms;android-21" \
        "system-images;android-34;google_apis;x86_64" \
        "emulator"

# Install .NET MAUI workload
RUN dotnet workload update \
    && dotnet workload install maui android

# Set proper permissions
RUN chmod -R 755 $ANDROID_HOME

# Set working directory
WORKDIR /workspace

# Expose common development ports
EXPOSE 5000 5001 8080
